 % Coverage report from v8
-------------------|---------|----------|---------|---------|-------------------
File               | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
-------------------|---------|----------|---------|---------|-------------------
All files          |    0.29 |    33.87 |   32.23 |    0.29 |                   
 src               |       0 |        0 |       0 |       0 |                   
  App.tsx          |       0 |        0 |       0 |       0 | 1-86              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  main.tsx         |       0 |        0 |       0 |       0 | 1-14              
 src/api           |       0 |        0 |       0 |       0 |                   
  endpoints.api.ts |       0 |        0 |       0 |       0 | 1-180             
 ...i/game-service |       0 |        0 |       0 |       0 |                   
  ...ervice.api.ts |       0 |        0 |       0 |       0 | 1-764             
 ...i/user-service |       0 |        0 |       0 |       0 |                   
  ...ervice.api.ts |       0 |        0 |       0 |       0 | 1-213             
 ...s/sidebarIcons |       0 |      100 |     100 |       0 |                   
  Following.tsx    |       0 |      100 |     100 |       0 | 10-33             
  Friends.tsx      |       0 |      100 |     100 |       0 | 10-53             
  Home.tsx         |       0 |      100 |     100 |       0 | 10-40             
  Library.tsx      |       0 |      100 |     100 |       0 | 10-35             
  Search.tsx       |       0 |      100 |     100 |       0 | 10-40             
  Settings.tsx     |       0 |      100 |     100 |       0 | 10-40             
 src/enums         |       0 |        0 |       0 |       0 |                   
  ...type.enums.ts |       0 |        0 |       0 |       0 | 1-4               
  ...type.enums.ts |       0 |        0 |       0 |       0 | 1-8               
  ...lace.enums.ts |       0 |        0 |       0 |       0 | 1-9               
  friend.enums.ts  |       0 |        0 |       0 |       0 | 1-4               
  ...tent.enums.ts |       0 |        0 |       0 |       0 | 1-3               
  ...edia.enums.ts |       0 |        0 |       0 |       0 | 1-4               
  ...page.enums.ts |       0 |        0 |       0 |       0 | 1-15              
  ...ction.enum.ts |       0 |        0 |       0 |       0 | 1-9               
  ...vents.enum.ts |       0 |        0 |       0 |       0 | 1-13              
  storage.enums.ts |       0 |        0 |       0 |       0 | 1-2               
  theme.enum.ts    |       0 |        0 |       0 |       0 | 1-2               
  user.enum.ts     |       0 |        0 |       0 |       0 | 1-9               
 src/hooks         |       0 |        0 |       0 |       0 |                   
  redux.hooks.ts   |       0 |        0 |       0 |       0 | 1-6               
  ...teControl.tsx |       0 |        0 |       0 |       0 | 1-55              
  useWebSocket.ts  |       0 |        0 |       0 |       0 | 1-135             
 ...les/components |       0 |    33.33 |   33.33 |       0 |                   
  Components.tsx   |       0 |        0 |       0 |       0 | 1-187             
  data.ts          |       0 |      100 |     100 |       0 | 2-135             
  style.ts         |       0 |        0 |       0 |       0 | 1-3               
 ...es/follow-page |       0 |        0 |       0 |       0 |                   
  FollowPage.tsx   |       0 |        0 |       0 |       0 | 1-59              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-3               
 ...es/friend-page |       0 |        0 |       0 |       0 |                   
  FriendPage.tsx   |       0 |        0 |       0 |       0 | 1-59              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-3               
 ...s/game-details |       0 |        0 |       0 |       0 |                   
  GameDetails.tsx  |       0 |        0 |       0 |       0 | 1-494             
 ...ils/components |       0 |        0 |       0 |       0 |                   
  GameOverview.tsx |       0 |        0 |       0 |       0 | 1-243             
  style.ts         |       0 |        0 |       0 |       0 | 1-75              
 ...e-interstitial |       0 |        0 |       0 |       0 |                   
  ...erstitial.tsx |       0 |        0 |       0 |       0 | 1-87              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-24              
 ...terstitialInfo |       0 |        0 |       0 |       0 |                   
  ...itialInfo.tsx |       0 |        0 |       0 |       0 | 1-146             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
 src/modules/home  |       0 |        0 |       0 |       0 |                   
  Home.tsx         |       0 |        0 |       0 |       0 | 1-285             
  style.ts         |       0 |        0 |       0 |       0 | 1-260             
 ...odules/library |       0 |        0 |       0 |       0 |                   
  LibraryPage.tsx  |       0 |        0 |       0 |       0 | 1-723             
 ...rofile-details |       0 |        0 |       0 |       0 |                   
  ...leDetails.tsx |       0 |        0 |       0 |       0 | 1-305             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-8               
 ...ts/ProfileHero |       0 |    33.33 |   33.33 |       0 |                   
  ProfileHero.tsx  |       0 |      100 |     100 |       0 | 3-122             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-44              
 .../publisherPage |       0 |        0 |       0 |       0 |                   
  FolloSection.tsx |       0 |        0 |       0 |       0 | 1-51              
  ...isherPage.tsx |       0 |        0 |       0 |       0 | 1-572             
 src/routes        |       0 |        0 |       0 |       0 |                   
  RouteList.tsx    |       0 |        0 |       0 |       0 | 1-65              
  Routing.tsx      |       0 |        0 |       0 |       0 | 1-14              
  path.ts          |       0 |        0 |       0 |       0 | 1-36              
 src/shared        |       0 |        0 |       0 |       0 |                   
  Asset.tsx        |       0 |        0 |       0 |       0 | 1-89              
 ...ared/IconGroup |       0 |    33.33 |   33.33 |       0 |                   
  IconGroup.tsx    |       0 |      100 |     100 |       0 | 2-17              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-20              
 ...shared/appIdle |       0 |        0 |       0 |       0 |                   
  AppIdle.tsx      |       0 |        0 |       0 |       0 | 1-192             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-35              
 ...mediaTileBadge |       0 |    33.33 |   33.33 |       0 |                   
  ...TIleBadge.tsx |       0 |      100 |     100 |       0 | 2-18              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-12              
 ...shared/banner2 |       0 |        0 |       0 |       0 |                   
  ...mbre.style.ts |       0 |        0 |       0 |       0 | 1-69              
 ...overlayContent |       0 |    66.66 |   66.66 |       0 |                   
  ...tDyanamic.tsx |       0 |      100 |     100 |       0 | 2-99              
  GameContent.tsx  |       0 |        0 |       0 |       0 | 1-111             
  VideoDyanmic.tsx |       0 |      100 |     100 |       0 | 2-101             
 ...nner2/standard |       0 |        0 |       0 |       0 |                   
  ...ardBanner.tsx |       0 |        0 |       0 |       0 | 1-333             
 ...s/bannerLayout |       0 |       50 |      50 |       0 |                   
  BannerMedia.tsx  |       0 |      100 |     100 |       0 | 2-23              
  ...yout.style.ts |       0 |        0 |       0 |       0 | 1-64              
  bannerLayout.tsx |       0 |      100 |     100 |       0 | 2-39              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
 ...ers/components |       0 |       50 |      50 |       0 |                   
  ...ayContent.tsx |       0 |      100 |     100 |       0 | 2-40              
  ...erlayMask.tsx |       0 |      100 |     100 |       0 | 2-32              
  ...tent.style.ts |       0 |        0 |       0 |       0 | 1-32              
  ...Mask.style.ts |       0 |        0 |       0 |       0 | 1-77              
 ...anners/dynamic |       0 |      100 |     100 |       0 |                   
  DynamicHero.tsx  |       0 |      100 |     100 |       0 | 2-93              
 ...ic/heroOverlay |       0 |      100 |     100 |       0 |                   
  ...moOverlay.tsx |       0 |      100 |     100 |       0 | 2-51              
  ...eoOverlay.tsx |       0 |      100 |     100 |       0 | 2-60              
 ...overlayContent |       0 |       60 |      60 |       0 |                   
  ...tDyanamic.tsx |       0 |      100 |     100 |       0 | 2-98              
  GameDynamic.tsx  |       0 |        0 |       0 |       0 | 1-144             
  ...amic.style.ts |       0 |        0 |       0 |       0 | 1-11              
  ...erDynamic.tsx |       0 |      100 |     100 |       0 | 2-79              
  VideoDyanmic.tsx |       0 |      100 |     100 |       0 | 2-102             
 ...ers/heroBanner |       0 |        0 |       0 |       0 |                   
  HeroBanner.tsx   |       0 |        0 |       0 |       0 | 1-185             
  index.ts         |       0 |        0 |       0 |       0 | 1-11              
  style.ts         |       0 |        0 |       0 |       0 | 1-122             
 ...ner/components |       0 |       75 |      75 |       0 |                   
  ...avigation.tsx |       0 |      100 |     100 |       0 | 2-54              
  BannerSlides.tsx |       0 |      100 |     100 |       0 | 4-31              
  ...ewWrapper.tsx |       0 |        0 |       0 |       0 | 1-58              
  ...otWrapper.tsx |       0 |      100 |     100 |       0 | 2-38              
 ...roBanner/hooks |       0 |        0 |       0 |       0 |                   
  useAutoPlay.ts   |       0 |        0 |       0 |       0 | 1-40              
 .../banners/ombre |       0 |       40 |      40 |       0 |                   
  EventOmbre.tsx   |       0 |      100 |     100 |       0 | 2-97              
  ...mbre.style.ts |       0 |        0 |       0 |       0 | 1-69              
  GameOmbre.tsx    |       0 |        0 |       0 |       0 | 1-118             
  LibraryOmbre.tsx |       0 |        0 |       0 |       0 | 1-217             
  VideoOmbre.tsx   |       0 |      100 |     100 |       0 | 2-93              
 ...nners/standard |       0 |      100 |     100 |       0 |                   
  StandardHero.tsx |       0 |      100 |     100 |       0 | 3-166             
 ...overlayContent |       0 |    66.66 |   66.66 |       0 |                   
  ...tDyanamic.tsx |       0 |      100 |     100 |       0 | 2-100             
  GameStandard.tsx |       0 |        0 |       0 |       0 | 1-109             
  VideoDyanmic.tsx |       0 |      100 |     100 |       0 | 2-96              
 ...shared/buttons |       0 |        0 |       0 |       0 |                   
  index.ts         |       0 |        0 |       0 |       0 | 1-2               
 ...ons/iconButton |       0 |        0 |       0 |       0 |                   
  IconButton.tsx   |       0 |        0 |       0 |       0 | 1-94              
  ...tton.style.ts |       0 |        0 |       0 |       0 | 1-48              
 .../primaryButton |       0 |        0 |       0 |       0 |                   
  ...aryButton.tsx |       0 |        0 |       0 |       0 | 1-139             
  ...tton.style.ts |       0 |        0 |       0 |       0 | 1-129             
 ...hared/card-box |       0 |        0 |       0 |       0 |                   
  CardBox.tsx      |       0 |        0 |       0 |       0 | 1-50              
  style.ts         |       0 |        0 |       0 |       0 | 1-35              
 ...d/card-overlay |       0 |        0 |       0 |       0 |                   
  CardOverlay.tsx  |       0 |        0 |       0 |       0 | 1-15              
  style.ts         |       0 |        0 |       0 |       0 | 1-19              
 ...ed/carouselCol |       0 |        0 |       0 |       0 |                   
  CarouselCol.tsx  |       0 |        0 |       0 |       0 | 1-321             
  style.ts         |       0 |        0 |       0 |       0 | 1-47              
 ...ed/carouselRow |       0 |        0 |       0 |       0 |                   
  CarouselRow.tsx  |       0 |        0 |       0 |       0 | 1-311             
  style.ts         |       0 |        0 |       0 |       0 | 1-30              
 .../category-card |       0 |        0 |       0 |       0 |                   
  CategoryCard.tsx |       0 |        0 |       0 |       0 | 1-45              
  style.ts         |       0 |        0 |       0 |       0 | 1-19              
 ...xedHeroWrapper |       0 |        0 |       0 |       0 |                   
  ...roWrapper.tsx |       0 |        0 |       0 |       0 | 1-24              
  ...roWrapper.tsx |       0 |        0 |       0 |       0 | 1-25              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  ...pper.style.ts |       0 |        0 |       0 |       0 | 1-29              
  style.ts         |       0 |        0 |       0 |       0 | 1-28              
 .../follower-list |       0 |        0 |       0 |       0 |                   
  FollowerList.tsx |       0 |        0 |       0 |       0 | 1-176             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-14              
 ...ed/friend-list |       0 |        0 |       0 |       0 |                   
  FriendList.tsx   |       0 |        0 |       0 |       0 | 1-179             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-13              
 ...ared/game-tile |       0 |        0 |       0 |       0 |                   
  GameTile.tsx     |       0 |        0 |       0 |       0 | 1-112             
  style.ts         |       0 |        0 |       0 |       0 | 1-36              
 ...ared/gameBadge |       0 |        0 |       0 |       0 |                   
  GameBadge.tsx    |       0 |        0 |       0 |       0 | 1-76              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-30              
 ...red/image-card |       0 |        0 |       0 |       0 |                   
  ImageCard.tsx    |       0 |        0 |       0 |       0 | 1-45              
 ...red/imageVideo |       0 |        0 |       0 |       0 |                   
  ImageVideo.tsx   |       0 |        0 |       0 |       0 | 1-134             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-34              
 ...ts/KeyboardKey |       0 |    33.33 |   33.33 |       0 |                   
  KeyboardKey.tsx  |       0 |      100 |     100 |       0 | 2-37              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-30              
 ...yboardAlphaNum |       0 |       20 |      20 |       0 |                   
  ...lKeyboard.tsx |       0 |        0 |       0 |       0 | 1-67              
  ...ardAlpNum.tsx |       0 |        0 |       0 |       0 | 1-81              
  ...sKeyboard.tsx |       0 |      100 |     100 |       0 | 2-83              
  index.ts         |       0 |        0 |       0 |       0 | 1-3               
  style.ts         |       0 |        0 |       0 |       0 | 1-47              
 src/shared/layout |       0 |    33.33 |   33.33 |       0 |                   
  Layout.tsx       |       0 |      100 |     100 |       0 | 2-32              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  layout.style.ts  |       0 |        0 |       0 |       0 | 1-13              
 ...list-item-card |       0 |        0 |       0 |       0 |                   
  ListItemCard.tsx |       0 |        0 |       0 |       0 | 1-164             
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-39              
 src/shared/pill   |       0 |        0 |       0 |       0 |                   
  ...Container.tsx |       0 |        0 |       0 |       0 | 1-30              
  style.ts         |       0 |        0 |       0 |       0 | 1-34              
 ...ill/components |       0 |        0 |       0 |       0 |                   
  Pill.tsx         |       0 |        0 |       0 |       0 | 1-23              
 ...ed/profileIage |       0 |       25 |      25 |       0 |                   
  Kids.tsx         |       0 |      100 |     100 |       0 | 2-12              
  ProfileImage.tsx |       0 |        0 |       0 |       0 | 1-59              
  kidsStyle.ts     |       0 |        0 |       0 |       0 | 1-15              
  style.ts         |       0 |        0 |       0 |       0 | 1-24              
 ...red/promo-card |       0 |        0 |       0 |       0 |                   
  PromoCard.tsx    |       0 |        0 |       0 |       0 | 1-154             
  style.ts         |       0 |        0 |       0 |       0 | 1-33              
 .../publisherInfo |       0 |    33.33 |   33.33 |       0 |                   
  ...Info.style.ts |       0 |        0 |       0 |       0 | 1-29              
  ...isherInfo.tsx |       0 |      100 |     100 |       0 | 2-43              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
 ...red/search-bar |       0 |    33.33 |   33.33 |       0 |                   
  SearchBar.tsx    |       0 |      100 |     100 |       0 | 2-44              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
  style.ts         |       0 |        0 |       0 |       0 | 1-49              
 ...red/separators |       0 |       50 |      50 |       0 |                   
  ...Separator.tsx |       0 |      100 |     100 |       0 | 2-25              
  index.ts         |       0 |        0 |       0 |       0 | 1                 
 ...shared/sidebar |       0 |        0 |       0 |       0 |                   
  Sidebar.tsx      |       0 |        0 |       0 |       0 | 1-151             
  style.ts         |       0 |        0 |       0 |       0 | 1-16              
 ...nents/menuItem |       0 |        0 |       0 |       0 |                   
  MenuItem.tsx     |       0 |        0 |       0 |       0 | 1-78              
  style.ts         |       0 |        0 |       0 |       0 | 1-75              
 ...skeletonLoader |       0 |       50 |      50 |       0 |                   
  ...tonLoader.tsx |       0 |      100 |     100 |       0 | 2-87              
  constants.ts     |       0 |      100 |     100 |       0 | 2-10              
  index.ts         |       0 |        0 |       0 |       0 | 1-2               
  style.ts         |       0 |        0 |       0 |       0 | 1-40              
 ...ed/star-rating |      80 |    88.88 |   66.66 |      80 |                   
  StarRating.tsx   |     100 |      100 |     100 |     100 |                   
  style.ts         |       0 |        0 |       0 |       0 | 1-22              
 ...ared/topNavbar |       0 |        0 |       0 |       0 |                   
  TopNavbar.tsx    |       0 |        0 |       0 |       0 | 1-44              
  style.ts         |       0 |        0 |       0 |       0 | 1-74              
 ...red/video-tile |       0 |        0 |       0 |       0 |                   
  VideoTile.tsx    |       0 |        0 |       0 |       0 | 1-95              
  style.ts         |       0 |        0 |       0 |       0 | 1-74              
 ...gement/context |       0 |        0 |       0 |       0 |                   
  ...enContext.tsx |       0 |        0 |       0 |       0 | 1-203             
  ThemeContext.tsx |       0 |        0 |       0 |       0 | 1-71              
 ...t/ModalContext |       0 |        0 |       0 |       0 |                   
  ModalContext.tsx |       0 |        0 |       0 |       0 | 1-14              
  ...lProvider.tsx |       0 |        0 |       0 |       0 | 1-44              
  index.ts         |       0 |        0 |       0 |       0 | 1-2               
  style.ts         |       0 |        0 |       0 |       0 | 1-3               
 ...nagement/redux |       0 |        0 |       0 |       0 |                   
  store.redux.ts   |       0 |        0 |       0 |       0 | 1-10              
 .../redux/actions |       0 |        0 |       0 |       0 |                   
  ...ile.action.ts |       0 |        0 |       0 |       0 | 1-28              
 ...redux/reducers |       0 |       50 |      50 |       0 |                   
  index.ts         |       0 |      100 |     100 |       0 | 2-8               
  ...le.reducer.ts |       0 |        0 |       0 |       0 | 1-34              
 src/styles        |       0 |        0 |       0 |       0 |                   
  fonts.ts         |       0 |        0 |       0 |       0 | 1-27              
  globalStyle.ts   |       0 |        0 |       0 |       0 | 1-78              
  sharedStyles.ts  |       0 |        0 |       0 |       0 | 1-134             
 src/styles/theme  |       0 |       80 |      80 |       0 |                   
  borderRadius.ts  |       0 |      100 |     100 |       0 | 2-39              
  colors.ts        |       0 |      100 |     100 |       0 | 3-17              
  darkTheme.ts     |       0 |      100 |     100 |       0 | 3-43              
  gradients.ts     |       0 |      100 |     100 |       0 | 2-14              
  typography.ts    |       0 |        0 |       0 |       0 | 1-157             
 src/types         |       0 |    66.66 |   66.66 |       0 |                   
  banner.types.ts  |       0 |      100 |     100 |       0 | 43-65             
  ...card.types.ts |       0 |        0 |       0 |       0 | 1                 
  theme.type.ts    |       0 |        0 |       0 |       0 |                   
 src/types/api     |       0 |        0 |       0 |       0 |                   
  paginated.api.ts |       0 |        0 |       0 |       0 |                   
 ...types/api/game |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
 ...types/api/user |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
  ....api.types.ts |       0 |        0 |       0 |       0 |                   
 src/types/reducer |       0 |        0 |       0 |       0 |                   
  ...ducer.type.ts |       0 |        0 |       0 |       0 |                   
  ...ducer.type.ts |       0 |        0 |       0 |       0 |                   
 src/utils         |       0 |    33.33 |   33.33 |       0 |                   
  ...vice.utils.ts |       0 |        0 |       0 |       0 | 1-122             
  dateUtils.ts     |       0 |        0 |       0 |       0 | 1-86              
  helpers.utils.ts |       0 |        0 |       0 |       0 | 1-80              
  numberUtils.ts   |       0 |      100 |     100 |       0 | 12-23             
  storage.utils.ts |       0 |        0 |       0 |       0 | 1-3               
  styleUtils.ts    |       0 |      100 |     100 |       0 | 2-8               
-------------------|---------|----------|---------|---------|-------------------

=============================== Coverage summary ===============================
Statements   : 0.29% ( 28/9596 )
Branches     : 33.87% ( 84/248 )
Functions    : 32.23% ( 78/242 )
Lines        : 0.29% ( 28/9596 )
================================================================================
