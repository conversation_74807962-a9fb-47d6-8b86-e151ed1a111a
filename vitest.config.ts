/// <reference types="vitest" />
import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./setup.ts'],
    css: true,
    reporters: ['verbose'],
    coverage: {
      reporter: ['text', 'text-summary', 'json', 'html'],
      exclude: ['node_modules/', 'src/__test__/', '**/*.d.ts', '**/*.config.*', 'build/', 'tizen/'],
      reportOnFailure: true,
    },
  },
  resolve: {
    alias: {
      shared: '/src/shared',
      modules: '/src/modules',
      components: '/src/components',
      utils: '/src/utils',
    },
  },
});
