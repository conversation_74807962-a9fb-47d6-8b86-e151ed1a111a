{"compilerOptions": {"declaration": true, "declarationDir": "types", "target": "es6", "module": "ESNext", "moduleResolution": "node", "lib": ["DOM", "ESNext"], "jsx": "react-jsx", "noEmit": true, "isolatedModules": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": "./src", "paths": {"*": ["*"], "modules/*": ["modules/*"], "components/*": ["components/*"], "utils/*": ["utils/*"]}, "allowJs": true, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true}, "include": ["src/**/*", "vite.config.ts", "vitest.config.ts", "setup.ts", "src/shared/banners/components/.ts"], "exclude": ["node_modules"], "types": ["node", "redux", "react-redux", "vitest/globals"]}