import React from 'react';
import { GroupWrapper, IconCircle } from './style';

interface IconGroupProps {
  // The component now accepts an array of any renderable React nodes
  icons?: React.ReactNode[];
}

export function IconGroup({ icons }: IconGroupProps) {
  return (
    <GroupWrapper>
      {icons?.map((icon, index) => (
        <IconCircle key={index}>{icon}</IconCircle>
      ))}
    </GroupWrapper>
  );
}
