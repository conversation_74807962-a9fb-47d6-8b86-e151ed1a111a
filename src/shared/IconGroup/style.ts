import styled from 'styled-components';

export const GroupWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
`;

export const IconCircle = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.bgColor};
  border: 1px solid ${({ theme }) => theme.textPrimary};
  display: flex;
  align-items: center;
  justify-content: center;
  /* Set color for the icons inside */
  color: ${({ theme }) => theme.textPrimary};
  font-size: 18px;
  svg {
    width: 20px;
    height: 20px;
  }
`;
