import {
  useFocusable,
  type FocusableComponentLayout,
} from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';
import { useTheme } from 'styled-components';
import { ButtonContainer, ButtonText, ButtonIconContainer } from './primaryButton.style';
import { CircularLoader, Row } from 'styles/sharedStyles';

interface IconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

interface ButtonProps {
  variant?: 'default' | 'hero' | 'promo';
  children: React.ReactNode;
  icon?: React.ComponentType<IconProps> | React.ReactNode | string | null;
  onClick?: () => void;
  onFocus?: (layout?: FocusableComponentLayout) => void;
  focusKey?: string;
  disabled?: boolean;
  width?: string;
  height?: string;
  borderRadius?: string;
  isLoading?: boolean;
  fontSize?: string;
  fontWeight?: string;
  isActive?: boolean;
  bgColor?: string;
}

export function PrimaryButton(props: ButtonProps) {
  const {
    variant = 'default',
    children,
    icon,
    onClick,
    onFocus,
    focusKey = 'button',
    disabled = false,
    width,
    isLoading,
    height,
    borderRadius,
    fontSize,
    fontWeight,
    isActive,
    bgColor,
  } = props;
  const theme = useTheme();
  const { ref, focused } = useFocusable({
    focusKey,
    onEnterPress: () => {
      if (!disabled && onClick) {
        onClick();
      }
    },
    onFocus: layout => {
      if (onFocus) {
        onFocus(layout);
      }
    },
  });

  const getIconColor = () => {
    if (focused) return theme.textInvert;
    return theme.textPrimary;
  };

  const getIconSize = () => {
    return variant === 'hero'
      ? { width: 40, height: 40 }
      : variant === 'promo'
        ? { width: 16, height: 21 }
        : { width: 48, height: 48 };
  };

  const renderIcon = () => {
    if (!icon) return null;

    if (React.isValidElement(icon)) {
      return <ButtonIconContainer>{icon}</ButtonIconContainer>;
    }

    const iconSize = getIconSize();

    // If it's a React component (TSX icon)
    if (typeof icon === 'function') {
      const IconComponent = icon as React.ComponentType<IconProps>;
      return (
        <ButtonIconContainer>
          <IconComponent width={iconSize?.width} height={iconSize?.height} fill={getIconColor()} />
        </ButtonIconContainer>
      );
    }

    // If it's a string (SVG path or image URL)
    if (typeof icon === 'string') {
      return (
        <ButtonIconContainer>
          <img src={icon} alt="button icon" width={iconSize?.width} height={iconSize?.height} />
        </ButtonIconContainer>
      );
    }

    return null;
  };

  return (
    <ButtonContainer
      ref={ref}
      $variant={variant}
      $focused={focused}
      $disabled={disabled}
      $width={width}
      $height={height}
      $borderRadius={borderRadius}
      onClick={!disabled ? onClick : undefined}
      $isActive={isActive}
      $bgColor={bgColor}
    >
      {isLoading ? (
        <Row align="center" justify="center" width="200px">
          <CircularLoader />
        </Row>
      ) : (
        <>
          {renderIcon()}
          <ButtonText $fontSize={fontSize} $fontWeight={fontWeight} $variant={variant}>
            {children}
          </ButtonText>
        </>
      )}
    </ButtonContainer>
  );
}
