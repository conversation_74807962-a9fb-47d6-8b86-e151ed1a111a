import styled, { css } from 'styled-components';
import { activeStyle, defaultStyle } from 'styles/sharedStyles';
import { semanticBorderRadius } from 'styles/theme/borderRadius';

interface IButtonContainerProps {
  $variant: 'default' | 'hero' | 'promo';
  $focused: boolean;
  $disabled: boolean;
  $width?: string;
  $height?: string;
  $borderRadius?: string;
  $isActive?: boolean;
  $bgColor?: string;
}

interface IButtonTextProps {
  $variant: 'default' | 'hero' | 'promo';
  $focused?: boolean;
  $fontSize?: string;
  $fontWeight?: string;
}

export const ButtonText = styled.span<IButtonTextProps>`
  font-family:
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
  font-weight: 700;
  margin: 0;
  padding: 0;

  /* Typography based on variant */
  ${({ $variant, $fontSize, $fontWeight }) => {
    if ($variant === 'hero') {
      return css`
        font-size: 32px;
        line-height: 40px;
        /* Text/Button/LG */
      `;
    } else if ($variant === 'promo') {
      return css`
        font-size: 24px;
        line-height: 28px;
        /* Text/Button/LG */
      `;
    }
    return css`
      font-size: ${$fontSize ?? '40px'};
      font-weight: ${$fontWeight ?? '700'};
      line-height: 48px;
      /* Text/Button/XL */
    `;
  }}
`;

export const ButtonIconContainer = styled.div`
  display: flex;
  align-items: center;
  margin-right: 8px;

  svg,
  img {
    display: block;
  }
`;

export const ButtonContainer = styled.button<IButtonContainerProps>`
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: ${semanticBorderRadius.component.button};
  width: ${({ $width }) => $width || 'max-content'};
  height: ${({ $height }) => $height || 'max-content'};
  border-radius: ${({ $borderRadius }) => $borderRadius};

  /* Variant-specific styles */
  ${({ $variant }) => {
    if ($variant === 'hero') {
      return css`
        padding: 12px 40px;
      `;
    } else if ($variant === 'promo') {
      return css`
        padding: 4px 32px;

        /* Text/Button/LG */
      `;
    }
    return css`
      padding: 24px 40px;
    `;
  }}

  ${props =>
    props?.$focused || props?.$isActive
      ? activeStyle
      : css`
          ${defaultStyle}
          background: ${props?.$bgColor ?? props?.theme?.surfaceSecondaryOpacity100};
        `} 
        
  ${ButtonText} {
    ${props => (props?.$focused || props?.$isActive ? activeStyle : defaultStyle)}
    background: transparent;
  }

  &:hover {
    ${({ $disabled }) =>
      !$disabled &&
      css`
        ${activeStyle}
        ${ButtonText} {
          color: ${props => props?.theme?.bgColor};
        }
      `}
  }

  ${({ $disabled, theme }) =>
    $disabled &&
    css`
      opacity: 0.5;
      cursor: not-allowed;
      background-color: ${theme.surfaceSecondaryOpacity40};
    `}
`;
