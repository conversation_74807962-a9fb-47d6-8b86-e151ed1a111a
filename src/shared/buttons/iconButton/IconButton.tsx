import {
  useFocusable,
  type FocusableComponentLayout,
} from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';
import { useTheme } from 'styled-components';
import { IconButtonContainer, IconButtonIconContainer } from './iconButton.style';
import { CircularLoader } from 'styles/sharedStyles';

interface IconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

interface IconButtonProps {
  icon: React.ComponentType<IconProps> | React.ReactNode | string;
  onClick?: () => void;
  onFocus?: (layout?: FocusableComponentLayout) => void;
  focusKey?: string;
  width?: string;
  disabled?: boolean;
  isLoading?: boolean;
}

export function IconButton({
  icon,
  onClick,
  onFocus,
  focusKey = 'icon-button',
  disabled = false,
  width,
  isLoading,
}: IconButtonProps) {
  const theme = useTheme();
  const { ref, focused } = useFocusable({
    focusKey,
    onEnterPress: () => {
      if (!disabled && onClick) {
        onClick();
      }
    },
    onFocus: layout => {
      if (onFocus) {
        onFocus(layout);
      }
    },
  });

  const getIconColor = () => {
    return theme.textPrimary;
  };

  const renderIcon = () => {
    // If it's a React component (TSX icon)

    if (React.isValidElement(icon)) {
      return <IconButtonIconContainer>{icon}</IconButtonIconContainer>;
    }

    if (typeof icon === 'function') {
      const IconComponent = icon as React.ComponentType<IconProps>;
      return (
        <IconButtonIconContainer>
          <IconComponent height={34} fill={getIconColor()} />
        </IconButtonIconContainer>
      );
    }

    // If it's a string (SVG path or image URL)
    if (typeof icon === 'string') {
      return (
        <IconButtonIconContainer>
          <img src={icon} alt="icon button" height={34} />
        </IconButtonIconContainer>
      );
    }

    return null;
  };

  return (
    <IconButtonContainer
      ref={ref}
      width={width ?? '80px'}
      $focused={focused}
      $disabled={disabled}
      onClick={!disabled ? onClick : undefined}
    >
      {isLoading ? <CircularLoader /> : renderIcon()}
    </IconButtonContainer>
  );
}
