import styled, { css } from 'styled-components';

interface IconButtonContainerProps {
  $focused: boolean;
  $disabled: boolean;
  width: string;
}

export const IconButtonContainer = styled.button<IconButtonContainerProps>`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: ${({ width }) => width ?? '80px'};
  height: ${({ width }) => width ?? '80px'};
  flex-shrink: 0;
  aspect-ratio: 1/1;
  border: none;
  background-color: ${({ theme }) => theme.surfaceSecondaryOpacity100};
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 50%;

  /* Focus state - show border */
  ${({ theme, $focused }) =>
    $focused &&
    css`
      outline: 2px solid ${theme.outlineStrokeFocusWhite};
    `}

  /* Disabled state */
  ${({ $disabled }) =>
    $disabled &&
    `
      opacity: 0.5;
      cursor: not-allowed;
    `}
  
  &:hover {
    ${({ $disabled, theme }) =>
      !$disabled &&
      css`
        outline: 2px solid ${theme.outlineStrokeFocusWhite};
      `}
  }
`;

export const IconButtonIconContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  svg,
  img {
    display: block;
  }
`;
