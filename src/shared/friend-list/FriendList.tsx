import { useCallback, useContext, useEffect, useState } from 'react';
import { FriendWrap } from './style';
import { Row } from 'styles/sharedStyles';
import { PrimaryButton } from 'shared/buttons';
import { ButtonXL } from 'styles/theme/typography';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { SearchBar } from 'shared/search-bar';
import { ListItemCard } from 'shared/list-item-card/ListItemCard';
import CarouselCol from 'shared/carouselCol/CarouselCol';
import {
  acceptFriendRequest,
  getFriendRequestList,
  getFriendsList,
  rejectFriendRequest,
} from 'api/user-service/user-service.api';
import { useNavigate } from 'react-router-dom';
import { friendsRequestListPath, profileRoute } from 'routes/path';
import { ModalContext } from 'state-management/context/ModalContext';

interface IFriendList {
  isModal?: boolean;
  searchText?: string;
}

export const FriendList = (props: IFriendList) => {
  const { isModal, searchText } = props;
  const { hideModal } = useContext(ModalContext);
  const [showMyFriends, setShowMyFriends] = useState(true);

  // Add state to trigger refetch when searchText changes
  const [refetchTrigger, setRefetchTrigger] = useState(false);

  const { focusKey, focusSelf, ref } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: isModal ? 'friend-mod-my-friend' : undefined,
  });

  const navigate = useNavigate();

  useEffect(() => {
    const timer = setTimeout(() => {
      focusSelf();
    }, 300);

    return () => clearTimeout(timer);
  }, [focusSelf]);

  // Add effect to trigger refetch when searchText changes
  useEffect(() => {
    if (searchText !== undefined) {
      setRefetchTrigger(true);
    }
  }, [searchText]);

  const onColFocus = useCallback(
    ({ y }: { y: number }) => {
      (ref.current as HTMLDivElement)?.scrollTo({
        top: y,
        behavior: 'smooth',
      });
    },
    [ref]
  );

  // Callback to reset refetch trigger
  const handleRefetchComplete = useCallback(() => {
    setRefetchTrigger(false);
  }, []);

  return (
    <FocusContext.Provider value={focusKey}>
      <FriendWrap ref={ref} isModal={isModal}>
        {isModal && (
          <Row justify="center">
            <ButtonXL>Friends</ButtonXL>
          </Row>
        )}
        <Row gap="40px">
          <PrimaryButton
            focusKey="friend-mod-my-friend"
            isActive={showMyFriends}
            onClick={() => setShowMyFriends(true)}
            borderRadius="8px"
            height="72px"
            width="100%"
            fontSize={'32px'}
          >
            My Friends
          </PrimaryButton>
          <PrimaryButton
            focusKey="friend-mod-request"
            isActive={!showMyFriends}
            onClick={() => setShowMyFriends(false)}
            borderRadius="8px"
            height="72px"
            width="100%"
            fontSize={'32px'}
          >
            Requests
          </PrimaryButton>
        </Row>

        {isModal && (
          <SearchBar
            variant="outlined"
            focusKey="friend-mod-search"
            onClick={() => {
              navigate(friendsRequestListPath);
              hideModal();
            }}
          />
        )}

        {showMyFriends ? (
          <CarouselCol
            handleApiCall={async page => {
              return await getFriendsList(searchText, page);
            }}
            gap="40px"
            key={`friend-list-${searchText || 'all'}`} // Add searchText to key to force re-render
            renderItem={user => (
              <ListItemCard
                key={user?.id}
                title={user.friend_display_name}
                imageSrc={user?.friend_dp_url}
                isOnline
                onClick={() => {
                  navigate(`${profileRoute}/${user?.friend_id}`);
                  hideModal();
                }}
              />
            )}
            onFocus={onColFocus}
            skeletonVariant={'LIST_ITEM'}
            skeletonCount={10}
            skeletonGap="40px"
            isSkeletonCol
            refetchTrigger={refetchTrigger}
            onRefetchComplete={handleRefetchComplete}
            noDataMessage="No friend available"
          />
        ) : (
          <CarouselCol
            handleApiCall={async page => {
              return await getFriendRequestList(searchText, page);
            }}
            gap="40px"
            key={`friend-request-list-${searchText || 'all'}`} // Add searchText to key to force re-render
            renderItem={user => (
              <ListItemCard
                key={user?.id}
                isExpandable
                imageSrc={user?.friend_dp_url}
                title={user.friend_display_name}
                isOnline
                onAcceptClick={async () => {
                  await acceptFriendRequest(user?.id);
                }}
                onDenyClick={async () => {
                  await rejectFriendRequest(user?.id);
                }}
              />
            )}
            onFocus={onColFocus}
            skeletonVariant={'LIST_ITEM'}
            skeletonCount={10}
            skeletonGap="40px"
            isSkeletonCol
            refetchTrigger={refetchTrigger}
            onRefetchComplete={handleRefetchComplete}
            noDataMessage="No friend request available"
          />
        )}
      </FriendWrap>
    </FocusContext.Provider>
  );
};
