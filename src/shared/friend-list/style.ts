import styled from 'styled-components';
import { Col } from 'styles/sharedStyles';

interface IFriendWrap {
  isModal?: boolean;
}

export const FriendWrap = styled(Col)<IFriendWrap>`
  padding: ${props => (props?.isModal ? '40px 32px' : '0px')};
  gap: 60px;
  max-width: ${props => (props?.isModal ? '740px' : '856px')};
  border-radius: 16px;
  background: ${props => props?.theme?.bgColorOpacity90};
  backdrop-filter: blur(8px);
  height: 952px;
`;
