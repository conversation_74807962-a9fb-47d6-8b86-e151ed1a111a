import React, { forwardRef } from 'react';
import { ContentSection, HeroSection, PageContainer } from './style';

interface PageWrapperProps {
  hero?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  'data-scroll-container'?: string;
}

const FixedHeroWrapper = forwardRef<HTMLDivElement, PageWrapperProps>(
  ({ hero, children, className, ...props }, ref) => {
    return (
      <PageContainer ref={ref} className={className} {...props}>
        {hero && <HeroSection>{hero}</HeroSection>}
        <ContentSection>{children}</ContentSection>
      </PageContainer>
    );
  }
);

FixedHeroWrapper.displayName = 'FixedHeroWrapper';

export default FixedHeroWrapper;
