import styled from 'styled-components';

export const PageWrapperContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

export const HeroSection = styled.div`
  z-index: 0;
  transition: all 0.3s ease;
`;

interface ScrollableContProps {
  $removeTransform?: boolean;
  $customTransformValue?: string;
}

export const ScrollableCont = styled.div<ScrollableContProps>`
  overflow-y: auto;
  overflow-x: hidden;
  flex-shrink: 1;
  flex-grow: 1;
  z-index: 1;
  min-height: 500px;
  height: 100%;
  transform: ${({ $customTransformValue }) => `translateY( ${$customTransformValue ?? '-90px'})`} ;
  }};
`;
