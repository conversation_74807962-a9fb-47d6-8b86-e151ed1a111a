import styled from 'styled-components';

export const PageContainer = styled.div`
  position: relative;
  min-height: 100vh;
  width: 100%;
  overflow-y: auto;
`;

export const HeroSection = styled.section`
  // position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: inherit;
  z-index: 1;
  display: flex;
  flex-direction: column;
`;

export const ContentSection = styled.section`
  position: relative;
  z-index: 2;
  // margin-top: 800px;
  min-height: calc(100vh - 800px);
  width: 100%;
  padding: 0px 48px 0px 32px;
  background-color: ${({ theme }) => theme.bgColor};
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // some space at the bottom of the page
`;
