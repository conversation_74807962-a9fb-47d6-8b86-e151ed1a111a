import { HeroSection, PageWrapperContainer, ScrollableCont } from './stickyHeroWrapper.style';

const StickyHeroWrapper = (props: {
  heroSection: React.ReactNode;
  scrollableSection: React.ReactNode;
  removeTransform?: boolean;
  customTransformValue?: string;
}) => {
  const { heroSection, scrollableSection, removeTransform, customTransformValue } = props;

  return (
    <PageWrapperContainer>
      <HeroSection>{heroSection}</HeroSection>

      <ScrollableCont
        $removeTransform={removeTransform}
        $customTransformValue={customTransformValue}
      >
        {scrollableSection}
      </ScrollableCont>
    </PageWrapperContainer>
  );
};

export default StickyHeroWrapper;
