import {
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
  type KeyPressDetails,
} from '@noriginmedia/norigin-spatial-navigation';

import { CardBox } from 'shared/card-box/CardBox';
import { CardOverlay } from 'shared/card-overlay/CardOverlay';
// Import the new StyledVideo component
import { ESRBRating, RankIndex, TileContent, TileWrapper } from './style';
import { HeadingMD } from 'styles/theme/typography';

interface GameTileProps {
  index: number;
  name: string;
  gameSlug: string;
  imageUrl?: string;
  videoUrl?: string; // New prop for the video link
  starRating?: number;
  esrbRating?: string;
  isVertical?: boolean;
  focusKey?: string; // Accept focus key from parent
  onEnterPress?: (props: object, details: KeyPressDetails) => void;
  onFocus?: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  onCardFocused?: (props: object) => void; // New callback for when focused
}

export function GameTile({
  index,
  name,
  imageUrl,
  gameSlug,
  videoUrl, // Get the new prop
  starRating = 0,
  esrbRating,
  isVertical = false,
  focusKey, // Get the focus key from parent
  onEnterPress,
  onFocus,
  onCardFocused,
}: GameTileProps) {
  const { ref, focused } = useFocusable({
    focusKey: focusKey || `${name}-${index}`, // Use provided focus key or fallback
    onEnterPress,
    onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => {
      // Call the original onFocus callback
      onFocus?.(layout, props, details);

      // Call the onInit callback when focused
      if (onCardFocused) {
        onCardFocused(props);
      }
    },
    extraProps: {
      name,
      starRating,
      esrbRating,
      imageUrl,
      videoUrl, // Pass the new prop here
      index,
      isVertical,
      gameSlug,
    },
  });

  // Conditionally set dimensions based on the isVertical prop
  const width = isVertical ? '264px' : '412px';
  const height = isVertical ? '330px' : '240px';
  console.log(gameSlug, 'game');

  return (
    <TileWrapper ref={ref}>
      <CardBox
        borderRadius="0"
        focused={focused}
        imageUrl={imageUrl}
        width={width}
        height={height}
        videoUrl={videoUrl}
      >
        {/* ---- VIDEO PLAYER LOGIC START ---- */}
        {/* Render the video only when the tile is focused and a videoUrl exists */}
        {/* {focused && videoUrl && (
          <StyledVideo
            // The key forces the video element to re-mount if the src changes
            key={videoUrl}
            src={videoUrl}
            autoPlay
            muted
            loop
            playsInline
          />
        )} */}
        {/* ---- VIDEO PLAYER LOGIC END ---- */}

        <CardOverlay focused={focused} borderRadius="0" />
        {isVertical ? (
          // For vertical cards, show the rank index
          <RankIndex>
            <HeadingMD>{index + 1}</HeadingMD>
          </RankIndex>
        ) : (
          // For horizontal cards, show the original content
          <TileContent>
            {esrbRating && <ESRBRating alt="ESRB Rating" src={esrbRating} />}
          </TileContent>
        )}
      </CardBox>
    </TileWrapper>
  );
}
