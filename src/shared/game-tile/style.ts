import styled from 'styled-components';

export const TileWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

export const TileContent = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  width: 100%;
  z-index: 2;
`;

export const ESRBRating = styled.img`
  width: 38px;
  height: 54px;
`;
export const RankIndex = styled.div`
  position: absolute;
  top: 12px;
  left: 12px;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: ${prop => prop.theme.surfaceSecondaryOpacity40};
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px); /* The blur effect */
  -webkit-backdrop-filter: blur(4px);
  border: 2px solid ${prop => prop.theme.textPrimary};
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
`;
export const StyledVideo = styled.video`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover; /* This makes the video cover the entire container */
  z-index: 0; /* Ensure it's behind the overlay */
`;
