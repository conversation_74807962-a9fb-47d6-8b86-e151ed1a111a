import {
  useFocusable,
  KeyPressDetails,
} from "@noriginmedia/norigin-spatial-navigation";
import { StyledPill } from "../style";

interface PillProps {
  label: string;
  isActive: boolean;
  onSelect: (props: object, details: KeyPressDetails) => void;
}

export function Pill({ label, isActive, onSelect }: PillProps) {
  const { ref, focused } = useFocusable({
    onEnterPress: onSelect,
  });

  return (
    <StyledPill ref={ref} focused={focused} isActive={isActive}>
      {label}
    </StyledPill>
  );
}
