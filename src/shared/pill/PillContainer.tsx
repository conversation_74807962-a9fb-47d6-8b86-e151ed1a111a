import { useState } from "react";
import { Pill } from "./components/Pill";
import { ContainerWrapper } from "./style";

interface PillContainerProps {
  pills: string[];
  onPillSelect: (selectedPill: string) => void;
}

export function PillContainer({ pills, onPillSelect }: PillContainerProps) {
  const [activeIndex, setActiveIndex] = useState(0);

  const handleSelect = (index: number) => {
    setActiveIndex(index);
    onPillSelect(pills[index]);
  };

  return (
    <ContainerWrapper>
      {pills.map((label, index) => (
        <Pill
          key={label}
          label={label}
          isActive={index === activeIndex}
          onSelect={() => handleSelect(index)}
        />
      ))}
    </ContainerWrapper>
  );
}
