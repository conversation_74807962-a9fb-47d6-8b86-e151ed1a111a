import styled, { css } from 'styled-components';
import { LabelLG2 } from 'styles/theme/typography';

interface StyledPillProps {
  focused: boolean;
  isActive: boolean;
}

export const StyledPill = styled(LabelLG2)<StyledPillProps>`
  padding: 8px 40px;
  border-radius: 20px;
  cursor: pointer;
  transition:
    transform 0.2s ease,
    background-color 0.2s ease,
    color 0.2s ease;
  text-align: center;

  /* Style for the currently selected/active pill */
  ${({ isActive }) =>
    isActive
      ? css`
          background-color: ${prop => prop.theme.textPrimary};
          color: ${prop => prop.theme.textInvert};
        `
      : css`
          background-color: ${prop => prop.theme.surfaceSecondaryOpacity100};
          color: ${prop => prop.theme.iconWhite};
        `}

  /* Additional visual feedback for the currently focused pill */
    transform: ${({ focused }) => (focused ? 'scale(1.1)' : 'scale(1)')};
`;
export const ContainerWrapper = styled.div`
  display: flex;
  flex-direction: row;
  gap: 16px;
  padding: 12px;
`;
