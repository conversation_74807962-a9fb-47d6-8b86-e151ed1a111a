import { useCallback, useContext, useEffect, useState } from 'react';
import { FollowerWrap } from './style';
import { Row } from 'styles/sharedStyles';
import { PrimaryButton } from 'shared/buttons';
import { ButtonXL } from 'styles/theme/typography';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { SearchBar } from 'shared/search-bar';
import { ListItemCard } from 'shared/list-item-card/ListItemCard';
import CarouselCol from 'shared/carouselCol/CarouselCol';
import { getPubFollowing } from 'api/user-service/user-service.api';
import { useNavigate } from 'react-router-dom';
import { gameDetailsRoute, pubGameFollowListPath, publisherRoute } from 'routes/path';
import { ModalContext } from 'state-management/context/ModalContext';
import { getGameFollowers } from 'api/game-service/game-service.api';
import { useAppSelector } from 'hooks/redux.hooks';

interface IFollowerList {
  isModal?: boolean;
  searchText?: string;
}

export const FollowerList = (props: IFollowerList) => {
  const { isModal, searchText } = props;
  const { hideModal } = useContext(ModalContext);
  const { profile } = useAppSelector(state => state?.user);
  const [showGames, setShowGames] = useState(true);

  // Add state to trigger refetch when searchText changes
  const [refetchTrigger, setRefetchTrigger] = useState(false);

  const { focusKey, focusSelf, ref } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: 'follower-mod-games',
  });

  const navigate = useNavigate();

  useEffect(() => {
    const timer = setTimeout(() => {
      focusSelf();
    }, 300);

    return () => clearTimeout(timer);
  }, [focusSelf]);

  // Add effect to trigger refetch when searchText changes
  useEffect(() => {
    if (searchText !== undefined) {
      setRefetchTrigger(true);
    }
  }, [searchText]);

  const onColFocus = useCallback(
    ({ y }: { y: number }) => {
      (ref.current as HTMLDivElement)?.scrollTo({
        top: y,
        behavior: 'smooth',
      });
    },
    [ref]
  );

  // Callback to reset refetch trigger
  const handleRefetchComplete = useCallback(() => {
    setRefetchTrigger(false);
  }, []);

  return (
    <FocusContext.Provider value={focusKey}>
      <FollowerWrap ref={ref} isModal={isModal}>
        {isModal && (
          <Row justify="center">
            <ButtonXL>Following</ButtonXL>
          </Row>
        )}
        <Row gap="40px">
          <PrimaryButton
            focusKey="follower-mod-games"
            isActive={showGames}
            onClick={() => setShowGames(true)}
            borderRadius="8px"
            height="72px"
            width="100%"
            fontSize={'32px'}
          >
            Games
          </PrimaryButton>
          <PrimaryButton
            focusKey="follower-mod-publisher"
            isActive={!showGames}
            onClick={() => setShowGames(false)}
            borderRadius="8px"
            height="72px"
            width="100%"
            fontSize={'32px'}
          >
            Publishers
          </PrimaryButton>
        </Row>
        {isModal && (
          <SearchBar
            variant="outlined"
            focusKey="friend-mod-search"
            onClick={() => {
              navigate(pubGameFollowListPath);
              hideModal();
            }}
          />
        )}

        {showGames ? (
          <CarouselCol
            handleApiCall={async page => {
              return await getGameFollowers(searchText, page);
            }}
            gap="40px"
            key={`games-list-${searchText || 'all'}`} // Add searchText to key to force re-render
            renderItem={game => (
              <ListItemCard
                showOnline={false}
                key={game?.slug}
                title={game?.name ?? ''}
                imageSrc={game?.thumbnail ?? ''}
                isOnline
                isGame
                onClick={() => {
                  navigate(`${gameDetailsRoute}/${game?.slug}`);
                  hideModal();
                }}
              />
            )}
            onFocus={onColFocus}
            skeletonVariant={'LIST_ITEM'}
            skeletonCount={10}
            skeletonGap="40px"
            isSkeletonCol
            refetchTrigger={refetchTrigger}
            onRefetchComplete={handleRefetchComplete}
            noDataMessage="No game followed yet"
          />
        ) : (
          <CarouselCol
            handleApiCall={async page => {
              return await getPubFollowing(profile?.user?.id ?? '', searchText, page);
            }}
            gap="40px"
            key={`publisher-list-${searchText || 'all'}`} // Add searchText to key to force re-render
            renderItem={pub => (
              <ListItemCard
                showOnline={false}
                key={pub?.id}
                imageSrc={pub?.following_dp_url}
                title={pub?.following_display_name ?? ''}
                isOnline
                onClick={() => {
                  navigate(`${publisherRoute}/${pub?.following_id}`);
                  hideModal();
                }}
              />
            )}
            onFocus={onColFocus}
            skeletonVariant={'LIST_ITEM'}
            skeletonCount={10}
            skeletonGap="40px"
            isSkeletonCol
            refetchTrigger={refetchTrigger}
            onRefetchComplete={handleRefetchComplete}
            noDataMessage="No publisher followed yet"
          />
        )}
      </FollowerWrap>
    </FocusContext.Provider>
  );
};
