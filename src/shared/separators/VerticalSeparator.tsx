import React from 'react';
import styled from 'styled-components';

interface VerticalSeparatorProps {
  height?: string;
  width?: string;
  color?: string;
}

const SeparatorLine = styled.div<{ $height: string; $width: string; $color: string }>`
  width: ${({ $width }) => $width};
  height: ${({ $height }) => $height};
  background-color: ${({ $color }) => $color};
  display: inline-block;
`;

const VerticalSeparator: React.FC<VerticalSeparatorProps> = ({
  height = '20px',
  width = '1px',
  color = '#F5F5F5',
}) => {
  return <SeparatorLine $height={height} $width={width} $color={color} />;
};

export default VerticalSeparator;
