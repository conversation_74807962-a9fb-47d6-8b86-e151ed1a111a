import styled from "styled-components";

export const StarContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 2px;
  color: ${({ theme }) => theme.iconGold};
  font-size: 24px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  z-index: 2;
`;

export const StarIcon = styled.span`
  font-size: 24px;
`;

export const HalfStarIcon = styled.span`
  font-size: 24px;
  background: linear-gradient(
    90deg,
    ${({ theme }) => theme.iconGold} 50%,
    ${({ theme }) => theme.iconGold}4D 50%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`;
