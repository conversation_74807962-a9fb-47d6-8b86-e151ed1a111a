import styled, { css } from 'styled-components';
import { BodyXL } from 'styles/theme/typography';
import { I<PERSON>eyboardKey } from './KeyboardKey';

interface IKeyboardKeyWrap extends Partial<IKeyboardKey> {
  focused?: boolean;
}

export const KeyboardKeyWrap = styled.button<IKeyboardKeyWrap>`
  all: unset;
  height: ${props => props?.height ?? '60px'};
  width: ${props => props?.width ?? '56px'};
  border-radius: 8px;
  background: ${props =>
    props?.focused ? props?.theme?.surfaceWhite : props?.theme?.surfaceSecondaryOpacity100};

  display: inline-flex;
  align-items: center;
  justify-content: center;

  min-width: ${props => props?.minWidth};

  ${BodyXL} {
    ${props =>
      props?.fontSize &&
      css`
        font-size: ${props.fontSize};
      `}
    color: ${props =>
      !props?.focused ? props?.theme?.surfaceWhite : props?.theme?.surfaceSecondaryOpacity100};
    font-weight: 500;
  }
`;
