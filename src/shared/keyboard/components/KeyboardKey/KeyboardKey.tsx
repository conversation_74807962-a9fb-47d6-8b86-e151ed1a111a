import React from 'react';
import { KeyboardKeyWrap } from './style';
import { BodyXL } from 'styles/theme/typography';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';

export interface IKeyboardKey {
  label: string;
  onTap?: (key: string) => void;
  height?: string;
  width?: string;
  fontSize?: string;
  minWidth?: string;
}

export const KeyboardKey = (props: IKeyboardKey) => {
  const { label, fontSize, height, onTap, width, minWidth } = props;

  const { ref, focused } = useFocusable({
    focusKey: `keyboard-key-${label}`,
    onEnterPress: () => {
      onTap?.(label);
    },
  });

  return (
    <KeyboardKeyWrap
      ref={ref}
      focused={focused}
      fontSize={fontSize}
      height={height}
      width={width}
      minWidth={minWidth}
    >
      <BodyXL>{label}</BodyXL>
    </KeyboardKeyWrap>
  );
};
