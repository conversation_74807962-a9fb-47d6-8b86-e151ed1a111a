import React from 'react';
import { BottomRow, KeyboardContainer } from './style';
import { Row } from 'styles/sharedStyles';
import { KeyboardKey } from '../components/KeyboardKey';

// Interface for component props
interface ISpecialCharsKeyboardProps {
  onChange: (char: string) => void;
  onBackspace?: () => void;
  onToggleKeyboard?: () => void;
  onSpace?: () => void;
  onSearch?: () => void;
  onClear?: () => void;
}

export const SpecialCharsKeyboard: React.FC<ISpecialCharsKeyboardProps> = ({
  onChange,
  onBackspace,
  onToggleKeyboard,
  onSpace,
  onSearch,
  onClear,
}) => {
  // Special characters arranged in rows to match the image
  const topRowChars = ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')'];
  const bottomRowChars = ['-', '_', '\\', "'", '?', ',', '.', '<', '>', '[', ']'];

  const handleCharTap = (char: string) => {
    onChange(char);
  };

  return (
    <KeyboardContainer>
      {/* Top row with special characters and abc toggle */}
      <Row gap="8px" justify="center">
        {topRowChars.map((char, index) => (
          <KeyboardKey
            width="100%"
            key={`top-${char}-${index}`}
            onTap={() => handleCharTap(char)}
            label={char}
          />
        ))}
        <KeyboardKey width="100%" onTap={onToggleKeyboard} label="abc" fontSize="24px" />
      </Row>

      {/* Bottom row with more special characters */}
      <Row gap="8px" justify="center">
        {bottomRowChars.map((char, index) => (
          <KeyboardKey
            width="100%"
            key={`bottom-${char}-${index}`}
            onTap={() => handleCharTap(char)}
            label={char}
          />
        ))}
      </Row>

      {/* Action row with backspace, clear, space, and search */}
      <BottomRow>
        <KeyboardKey minWidth="56px" fontSize="24px" onTap={onBackspace} label={'⌫'} />

        <KeyboardKey width="100%" onTap={onClear} label={'Clear'} />

        <KeyboardKey width="100%" onTap={onSpace} label={'Space'} />

        <KeyboardKey width="100%" onTap={onSearch} label={'Search'} />

        {/* <ActionKey onClick={onClear} flex="1">
          Clear
        </ActionKey>

        <ActionKey onClick={onSpace} flex="1">
          Space
        </ActionKey>

        <ActionKey onClick={onSearch} flex="1">
          Search
        </ActionKey> */}
      </BottomRow>
    </KeyboardContainer>
  );
};
