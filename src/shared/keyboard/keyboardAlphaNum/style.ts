import styled from 'styled-components';

interface IKeyboardContainer {
  height?: string;
  width?: string;
}

export const KeyboardContainer = styled.div<IKeyboardContainer>`
  height: ${props => props.height};
  width: ${props => props.width};
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

export const KeyboardContent = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

export const KeysWrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
`;

export const BottomRow = styled.div`
  display: flex;
  gap: 12px;
`;

export const MainRow = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0;
`;

export const LeftSection = styled.div`
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-right: 12px;
`;

export const RightSection = styled.div`
  width: 200px;
  display: flex;
  flex-direction: column;
  gap: 12px;
`;
