import React, { useState } from 'react';
import { BottomRow, KeyboardContainer, KeysWrapper } from './style';
import { KeyboardKey } from '../components/KeyboardKey';

// Interface for component props
interface IAlphanumericKeyboardProps {
  onChange: (char: string) => void;
  onBackspace?: () => void;
  onToggleKeyboard?: () => void;
  onSpace?: () => void;
  onSearch?: () => void;
  onClear?: () => void;
}

export const AlphanumericKeyboard: React.FC<IAlphanumericKeyboardProps> = ({
  onChange,
  onBackspace,
  onToggleKeyboard,
  onSpace,
  onSearch,
  onClear,
}) => {
  const [isUpper, setIsUpper] = useState(false);

  // Generate alphanumeric characters (numbers 1-9, 0, then letters a-z or A-Z)
  const getAlphanumericCharacters = (): string[] => {
    const numbers = Array.from({ length: 9 }, (_, i) => `${i + 1}`);
    const zero = ['0'];
    const letters = Array.from({ length: 26 }, (_, i) =>
      String.fromCharCode((isUpper ? 65 : 97) + i)
    );

    return [...numbers, ...zero, ...letters];
  };

  const handleKeyTap = (char: string) => {
    onChange(char);
  };

  const handleToggleCase = () => {
    setIsUpper(!isUpper);
  };

  const alphanumericCharacters = getAlphanumericCharacters();

  return (
    <KeyboardContainer>
      <KeysWrapper>
        {alphanumericCharacters.map(char => (
          <KeyboardKey width="60px" key={char} label={char} onTap={() => handleKeyTap(char)} />
        ))}

        <KeyboardKey width="60px" onTap={handleToggleCase} label={'⇧'} />

        <KeyboardKey width="60px" onTap={onToggleKeyboard} fontSize={'24px'} label="!#$" />

        <KeyboardKey onTap={onBackspace} width={'132px'} label="⌫" />
      </KeysWrapper>

      <BottomRow>
        <KeyboardKey width="100%" onTap={onSpace} label="Space" />
        <KeyboardKey width="100%" onTap={onSearch} label="Search" />
        <KeyboardKey width="100%" onTap={onClear} label="Clear" />
      </BottomRow>
    </KeyboardContainer>
  );
};
