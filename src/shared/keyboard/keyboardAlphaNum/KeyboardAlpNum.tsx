import React, { useState } from 'react';
import { KeyboardContainer, KeyboardContent } from './style';
import { SpecialCharsKeyboard } from './SpecialCharsKeyboard';
import { AlphanumericKeyboard } from './AlphaNumericalKeyboard';

// Interface for component props
interface IKeyboardAlpNumProps {
  onChange: (text: string) => void;
  onSearch?: () => void;
  alpHeight?: string;
  alpWidth?: string;
  numHeight?: string;
  numWidth?: string;
}

export const KeyboardAlpNum: React.FC<IKeyboardAlpNumProps> = ({
  onChange,
  onSearch,
  alpHeight,
  alpWidth,
  numHeight,
  numWidth,
}) => {
  const [isSpecialChars, setIsSpecialChars] = useState(false);
  const [text, setText] = useState('');

  const updateText = (newChar: string) => {
    const newText = text + newChar;
    setText(newText);
    onChange(newText);
  };

  const onSpace = () => {
    updateText(' '); // Just add a single space character
  };

  const onBackspace = () => {
    const newText = text.length > 0 ? text.substring(0, text.length - 1) : '';
    setText(newText);
    onChange(newText);
  };

  const onClear = () => {
    setText('');
    onChange('');
  };

  const onToggleKeyboard = () => {
    setIsSpecialChars(!isSpecialChars);
  };

  // Determine current dimensions based on keyboard type
  const currentHeight = isSpecialChars ? numHeight : alpHeight;
  const currentWidth = isSpecialChars ? numWidth : alpWidth;

  return (
    <KeyboardContainer height={currentHeight} width={currentWidth}>
      <KeyboardContent>
        {isSpecialChars ? (
          <SpecialCharsKeyboard
            onChange={updateText}
            onToggleKeyboard={onToggleKeyboard}
            onBackspace={onBackspace}
            onClear={onClear}
            onSpace={onSpace}
            onSearch={onSearch}
          />
        ) : (
          <AlphanumericKeyboard
            onChange={updateText}
            onToggleKeyboard={onToggleKeyboard}
            onBackspace={onBackspace}
            onClear={onClear}
            onSpace={onSpace}
            onSearch={onSearch}
          />
        )}
      </KeyboardContent>
    </KeyboardContainer>
  );
};
