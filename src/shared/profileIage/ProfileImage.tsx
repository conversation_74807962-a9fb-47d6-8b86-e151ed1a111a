import React, { useState, useCallback } from 'react';
import ProfileIcon from 'assets/icons/profile.svg';
import { ImageContainer, ProfileImg, FallbackIcon } from './style';

interface ProfileImageProps {
  imageUrl?: string;
  alt?: string;
  size?: number;
  className?: string;
}

const ProfileImage: React.FC<ProfileImageProps> = ({
  imageUrl,
  alt = 'Profile',
  size = 64,
  className,
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
  }, []);

  const handleImageLoad = useCallback(() => {
    setHasError(false);
    setIsLoading(false);
  }, []);

  // If no imageUrl provided or there's an error, show fallback
  const shouldShowFallback = !imageUrl || hasError;

  return (
    <ImageContainer size={size} className={className}>
      {shouldShowFallback ? (
        <FallbackIcon src={ProfileIcon} alt={alt} size={size} />
      ) : (
        <ProfileImg
          src={imageUrl}
          alt={alt}
          size={size}
          onError={handleImageError}
          onLoad={handleImageLoad}
          style={{
            display: isLoading ? 'none' : 'block',
          }}
        />
      )}

      {/* Show fallback while loading if image URL exists */}
      {isLoading && imageUrl && !hasError && (
        <FallbackIcon src={ProfileIcon} alt={alt} size={size} />
      )}
    </ImageContainer>
  );
};

export default ProfileImage;
