import styled from 'styled-components';

export const ImageContainer = styled.div<{ size: number }>`
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  position: relative;
`;

export const ProfileImg = styled.img<{ size: number }>`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
`;

export const FallbackIcon = styled.img<{ size: number }>`
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  border-radius: 50%;
`;
