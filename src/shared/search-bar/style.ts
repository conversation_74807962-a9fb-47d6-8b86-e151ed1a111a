import styled, { css } from 'styled-components';
import { HeadingMD } from 'styles/theme/typography';

interface ISearchBarWrap {
  variant?: 'outlined' | 'standard';
  focused?: boolean;
  disabled?: boolean;
}

export const SearchBarWrap = styled.button<ISearchBarWrap>`
  all: unset;
  box-sizing: border-box;
  display: flex;

  justify-content: flex-start;
  align-items: flex-start;
  align-self: stretch;
  gap: 16px;

  ${props =>
    props?.variant == 'outlined'
      ? css`
          padding: 16px 40px;
          border-radius: 14px;
          height: 80px;
          background: ${props => props?.theme.surfaceSecondaryOpacity100};
        `
      : css`
          padding: 24px 16px;
          height: 96px;
          border-bottom: 4px solid ${props => props?.theme.iconWhite};
        `}

  ${props =>
    props?.disabled &&
    css`
      opacity: 0.3;
      pointer-events: none;
    `}     

    
  ${props =>
    props?.focused &&
    css`
      outline: 4px solid ${props => props?.theme.iconWhite};
    `}   

  ${HeadingMD} {
    color: ${props => props?.theme.iconWhite};
  }
`;
