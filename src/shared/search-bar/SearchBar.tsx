import React from 'react';
import { SearchBarWrap } from './style';
import { IoIosSearch } from 'react-icons/io';
import { HeadingMD } from 'styles/theme/typography';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';

interface ISearchBar {
  searchText?: string;
  placeholder?: string;
  variant?: 'outlined' | 'standard';
  focusKey?: string;
  disabled?: boolean;
  onClick?: () => void;
  isFocusable?: boolean;
}

export const SearchBar = (props: ISearchBar) => {
  const {
    placeholder,
    searchText,
    variant = 'standard',
    focusKey,
    disabled,
    onClick,
    isFocusable = true,
  } = props;

  const { ref, focused } = useFocusable({
    focusable: isFocusable,
    focusKey,
    onEnterPress: () => {
      if (!disabled && onClick) {
        onClick();
      }
    },
  });

  return (
    <SearchBarWrap variant={variant} ref={ref} focused={focused} disabled={disabled}>
      <IoIosSearch fontSize={48} />
      <HeadingMD>{searchText ? searchText : (placeholder ?? 'Search...')}</HeadingMD>
    </SearchBarWrap>
  );
};
