import styled from 'styled-components';

export const AppContainer = styled.div`
  background-color: ${({ theme }) => theme.bgColor};
  max-width: 1920px;
  max-height: 1080px;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
`;

export const Container = styled.main`
  display: grid;
  grid-template-columns: auto minmax(0, 1fr);
  grid-template-rows: 1fr;
  flex: 1;
  height: calc(1080px - 80px);
  position: relative;
  gap: 10px;
  overflow: hidden;
`;
