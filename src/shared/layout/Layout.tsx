import React from 'react';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { Sidebar } from 'shared/sidebar/Sidebar';
import { TopNavbar } from 'shared/topNavbar/TopNavbar';
import { AppContainer, Container } from './layout.style';

interface LayoutProps {
  children: React.ReactNode;
  currentPage: string;
  onNavigate: (page: string, path: string) => void;
}

export const Layout: React.FC<LayoutProps> = ({ children, currentPage, onNavigate }) => {
  const { ref, focusKey } = useFocusable({
    focusable: true,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <AppContainer ref={ref}>
        <TopNavbar />
        <Container>
          <Sidebar focusKey="MENU" currentPage={currentPage} onNavigate={onNavigate} />
          {children}
        </Container>
      </AppContainer>
    </FocusContext.Provider>
  );
};
