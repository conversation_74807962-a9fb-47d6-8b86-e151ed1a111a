import React from 'react';
import { IoCheckmark } from 'react-icons/io5';
import ProfileImage from 'shared/profileIage/ProfileImage';
import {
  PublisherInfoContainer,
  PublisherText,
  VerificationIcon,
  CheckmarkCircle,
} from './PublisherInfo.style';

interface PublisherInfoProps {
  imageUrl?: string;
  publisherName?: string;
  profileSize?: number;
  fontSize?: string;
  iconSize?: number;
  isVerified?: boolean;
}

const PublisherInfo: React.FC<PublisherInfoProps> = ({
  imageUrl = '',
  publisherName = 'Publisher',
  profileSize = 80,
  iconSize = 24,
  isVerified = false,
  fontSize,
}) => {
  return (
    <PublisherInfoContainer>
      <ProfileImage imageUrl={imageUrl} size={profileSize} />
      <PublisherText fontSize={fontSize}>{publisherName}</PublisherText>
      {isVerified && (
        <VerificationIcon>
          <CheckmarkCircle size={iconSize}>
            <IoCheckmark size={iconSize * 0.6} />
          </CheckmarkCircle>
        </VerificationIcon>
      )}
    </PublisherInfoContainer>
  );
};

export default PublisherInfo;
