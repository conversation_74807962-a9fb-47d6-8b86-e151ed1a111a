import styled from 'styled-components';
import { ButtonLG } from 'styles/theme/typography';

export const PublisherInfoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

export const PublisherText = styled(ButtonLG)<{ fontSize?: string }>`
  font-size: ${({ fontSize }) => fontSize};
  color: ${({ theme }) => theme.textPrimary};
  line-height: 1.2;
`;

export const VerificationIcon = styled.div`
  display: flex;
  align-items: center;
`;

export const CheckmarkCircle = styled.div<{ size?: number }>`
  width: ${({ size }) => size || 24}px;
  height: ${({ size }) => size || 24}px;
  background-color: ${({ theme }) => theme.outlineStrokeFocusTertiary};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.textPrimary};
`;
