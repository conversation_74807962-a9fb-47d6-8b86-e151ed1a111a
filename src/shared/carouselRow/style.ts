import styled from 'styled-components';
import { HeadingLG } from 'styles/theme/typography';

export const ContentRowWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 37px;
`;

export const ContentRowTitle = styled(HeadingLG)`
  margin-bottom: 22px;
`;

export const ContentRowScrollingWrapper = styled.div`
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
`;

export const ContentRowScrollingContent = styled.div`
  display: flex;
  flex-direction: row;
  gap: 32px;
  width: max-content;
  min-width: 100%;
  justify-content: flex-start;
`;
