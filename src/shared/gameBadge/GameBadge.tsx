import React from 'react';
import { FaUsers, FaHeart } from 'react-icons/fa';
import { GiGamepad } from 'react-icons/gi';
import { Row, VerticalLine } from 'styles/sharedStyles';
import { BodyXL } from 'styles/theme/typography';
import { BadgeWrap, Dot, IImageSection, ImageSection } from './style';

export interface IGameBadge {
  imageUrl: string;
  players?: string | number;
  followers?: string | number;
  likes?: string | number;
  rating: string;
  themes: string[];
  imageStyle: IImageSection;
}

export const GameBadge = (props: Partial<IGameBadge>) => {
  const {
    imageUrl,
    players = '0',
    followers = '0',
    likes = '0',
    rating,
    themes,
    imageStyle,
  } = props;

  return (
    <BadgeWrap>
      <ImageSection
        height={imageStyle?.height}
        maxWidth={imageStyle?.maxWidth}
        src={imageUrl}
        alt="game-text-image"
      />

      <Row margin="16px 0px">
        <Row gap="12px">
          <GiGamepad fontSize={32} />
          <BodyXL>{players}</BodyXL>
        </Row>
        <Row padding="0px 24px">
          <VerticalLine />
        </Row>

        <Row gap="12px">
          <FaUsers fontSize={32} />
          <BodyXL>{followers}</BodyXL>
        </Row>
        <Row padding="0px 24px">
          <VerticalLine />
        </Row>

        <Row gap="12px">
          <FaHeart fontSize={32} />
          <BodyXL>{likes}</BodyXL>
        </Row>
        <Row padding="0px 24px">
          <VerticalLine />
        </Row>

        {rating && <BodyXL>{rating}</BodyXL>}
      </Row>

      <Row gap="16px">
        {themes?.map((genre, index) => (
          <React.Fragment key={genre}>
            <BodyXL>{genre}</BodyXL>
            {index < themes.length - 1 && <Dot />}
          </React.Fragment>
        ))}
      </Row>
    </BadgeWrap>
  );
};
