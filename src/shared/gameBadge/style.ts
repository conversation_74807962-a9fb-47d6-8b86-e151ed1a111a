import styled from 'styled-components';
import { Row } from 'styles/sharedStyles';

export const BadgeWrap = styled.div`
  width: fit-content;
  border-radius: 12px;
  overflow: hidden;

  ${Row} {
    align-items: center;
  }
`;

export interface IImageSection {
  height?: string;
  maxWidth?: string;
}

export const ImageSection = styled.img<IImageSection>`
  width: fit-content;
  height: ${props => props.height ?? `104px`};
  object-fit: contain;
  max-width: ${props => props?.width ?? `560px`};
  width: 100%;
`;

export const Dot = styled.span`
  width: 16px;
  height: 16px;
  background-color: ${props => props?.theme?.surfaceWhite};
  border-radius: 50%;
  display: inline-block;
`;
