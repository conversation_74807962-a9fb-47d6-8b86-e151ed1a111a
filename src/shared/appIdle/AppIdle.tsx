import { BadgeWrap, Gradient, IdleImage, IdleWrap } from './style';
import { IScreenSaver } from 'types/api/game/screensaver.game.api.types';
import { useEffect, useRef, useState } from 'react';
import { ITrackIdleScreen } from 'types/api/game/track-idle-screen.game.api.types';
import { PAGE_ID } from 'enums/idle-page.enums';
import { trackIdleScreen } from 'api/game-service/game-service.api';
import { GameBadge } from 'shared/gameBadge';
import { IScreenSaverAnimationSetting } from 'types/api/game/screensaver-settings.game.api.types';
import { AnimatePresence } from 'framer-motion';

interface IAppIdle {
  screensaverList: IScreenSaver[];
  onScreenSaverEnd?: () => void;
  isLoop?: boolean;
  pageId?: PAGE_ID;
  animDuration?: IScreenSaverAnimationSetting;
}

const fadeVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { duration: 0.8 } },
  exit: { opacity: 0, transition: { duration: 0.8 } },
};

export const AppIdle = (props: IAppIdle) => {
  const { screensaverList, onScreenSaverEnd, isLoop = false, pageId, animDuration } = props;

  const [screenCounter, setScreenCounter] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState(false);
  const [totalScreenDisplayed, setTotalScreenDisplayed] = useState(0);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const [startTime, setStartTime] = useState('');
  const latestDataRef = useRef<ITrackIdleScreen | null>(null);

  const duration = animDuration?.duration ? animDuration.duration * 1000 : 8000;

  useEffect(() => {
    latestDataRef.current = {
      ended_at: new Date().toISOString().split('.')[0],
      interruption_image: screensaverList[screenCounter]?.image_id,
      no_of_screens_shown: totalScreenDisplayed,
      screen_type: pageId,
      started_at: startTime,
    };
  }, [screensaverList, screenCounter, totalScreenDisplayed, pageId, startTime]);

  useEffect(() => {
    return () => {
      if (latestDataRef.current) {
        trackIdleScreen(latestDataRef.current);
      }
    };
  }, []);

  useEffect(() => {
    setStartTime(new Date().toISOString().split('.')[0]);
  }, []);

  // Preload all images (both background and logo images)
  useEffect(() => {
    if (screensaverList?.length) {
      // Collect all image URLs (both background and logo)
      const allImageUrls: string[] = [];

      screensaverList.forEach(screen => {
        if (screen.screen_saver_image_url) {
          allImageUrls.push(screen.screen_saver_image_url);
        }
        if (screen.logo_url) {
          allImageUrls.push(screen.logo_url);
        }
      });

      // Remove duplicates and filter out empty values
      const uniqueImageUrls = [...new Set(allImageUrls)].filter(Boolean);

      if (uniqueImageUrls.length === 0) {
        setImagesLoaded(true);
        return;
      }

      const preloadPromises = uniqueImageUrls.map(url => {
        return new Promise(resolve => {
          const img = new Image();
          img.onload = () => {
            resolve(url);
          };
          img.onerror = error => {
            console.warn('Failed to load image:', url, error);

            resolve(url); // Resolve anyway to not block other images
          };
          img.src = url;
        });
      });

      Promise.allSettled(preloadPromises).then(() => {
        setImagesLoaded(true);
      });
    }
  }, [screensaverList]);

  useEffect(() => {
    if (screensaverList?.length && imagesLoaded) {
      intervalRef.current = setInterval(() => {
        setTotalScreenDisplayed(prev => prev + 1);
        setScreenCounter(prev => {
          const nextCounter = prev + 1;

          // If we've reached the end of the list
          if (nextCounter >= screensaverList.length) {
            if (isLoop) {
              // Loop back to the beginning
              return 0;
            } else {
              // Don't increment, stay at the last item
              return prev;
            }
          }

          return nextCounter;
        });
      }, duration);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [screensaverList, imagesLoaded, isLoop, duration]);

  useEffect(() => {
    if (screensaverList.length && imagesLoaded && !isLoop) {
      // Only trigger onScreenSaverEnd if not looping and we've reached the end
      if (screenCounter === screensaverList.length - 1) {
        // Wait for the current screensaver to display for 8 seconds, then end
        const endTimer = setTimeout(() => {
          onScreenSaverEnd?.();
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
        }, duration);

        return () => clearTimeout(endTimer);
      }
    }
  }, [screensaverList, screenCounter, onScreenSaverEnd, imagesLoaded, isLoop, duration]);

  const currentScreenSaver = screensaverList[screenCounter];

  return (
    <IdleWrap>
      <AnimatePresence mode="wait">
        <IdleImage
          key={currentScreenSaver?.screen_saver_image_url}
          variants={fadeVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          src={currentScreenSaver?.screen_saver_image_url ?? ''}
          custom={animDuration?.duration}
        />
        <Gradient />
        <BadgeWrap
          key={`badge-${currentScreenSaver?.logo_url}`}
          custom={animDuration?.textFadeDuration}
          variants={{
            ...fadeVariants,
            visible: (delay: number = 0) => ({
              opacity: 1,
              transition: { duration: 0.8, delay },
            }),
          }}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <GameBadge
            imageUrl={currentScreenSaver?.logo_url ?? ''}
            followers={currentScreenSaver?.followers_count}
            likes={currentScreenSaver?.fav_count}
            players={currentScreenSaver?.played_count}
            rating={currentScreenSaver?.description ?? ''}
            themes={currentScreenSaver?.themes ?? []}
          />
        </BadgeWrap>
      </AnimatePresence>
    </IdleWrap>
  );
};
