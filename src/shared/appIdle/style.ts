import { motion } from 'framer-motion';
import styled from 'styled-components';

interface IIdleWrap {
  url?: string;
}

export const IdleWrap = styled.div<IIdleWrap>`
  position: absolute;
  inset: 0px;
  z-index: 999;
  height: 100vh;
  width: 100vw;
  background: ${({ theme }) => theme.bgColor};
`;

export const Gradient = styled(motion.div)`
  position: absolute;
  inset: 0px;
  height: 100vh;
  width: 100vw;
  background: ${({ theme }) => theme.idleOverlayGradient};
`;

export const IdleImage = styled(motion.img)`
  height: 100%;
  width: 100%;
  object-fit: cover;
`;

interface I_BadgeWrap {
  delay?: boolean;
}

export const BadgeWrap = styled(motion.div)<I_BadgeWrap>`
  position: absolute;
  bottom: 56px;
  left: 56px;
`;
