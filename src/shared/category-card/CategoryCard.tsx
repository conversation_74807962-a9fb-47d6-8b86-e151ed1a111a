import {
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
  type KeyPressDetails,
} from '@noriginmedia/norigin-spatial-navigation';
import { CardBox } from 'shared/card-box/CardBox';
import { CardWrapper, CategoryName, CategoryOverlay } from './style';
import { CardOverlay } from 'shared/card-overlay/CardOverlay';

interface ICategoryCardProps {
  categoryName: string;
  imageUrl: string;
  onEnterPress?: (props: object, details: KeyPressDetails) => void;
  onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
}

export function CategoryCard({
  categoryName,
  imageUrl,
  onEnterPress,
  onFocus,
}: ICategoryCardProps) {
  const { ref, focused } = useFocusable({
    onEnterPress,
    onFocus,
    extraProps: { categoryName },
  });

  return (
    <CardWrapper ref={ref}>
      <CardBox
        focused={focused}
        imageUrl={imageUrl}
        width="323px"
        height="105px"
        borderRadius="16px"
      >
        <CardOverlay borderRadius="16px" focused={focused} />

        <CategoryName>{categoryName}</CategoryName>
      </CardBox>
    </CardWrapper>
  );
}
