import styled from 'styled-components';
import { HeadingMD } from 'styles/theme/typography';

// This wrapper is what the useFocusable ref will attach to.
export const CardWrapper = styled.div`
  border-radius: 16px;
`;

export const CategoryOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  border-radius: 10px; /* Slightly smaller than parent to stay inside border */
`;

export const CategoryName = styled(HeadingMD)`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
  z-index: 2;
  white-space: nowrap; /* Prevents text from wrapping */
`;
