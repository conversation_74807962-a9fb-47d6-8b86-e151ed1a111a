import React, { useState, useCallback, useEffect } from 'react';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { BannerType } from 'enums/banner-type.enums';
import { GAME_MEDIA } from 'enums/game-media.enums';
import { ContentType } from 'types/focused-card.types';

import BannerOverlayContent from 'shared/banners/components/BannerOverlayContent';
import DynamicPromoOverlay from 'shared/banners/dynamic/heroOverlay/DynamicPromoOverlay';
import DynamicVideoOverlay from 'shared/banners/dynamic/heroOverlay/DynamicVideoOverlay';
import GameStandard from 'shared/banners/standard/overlayContent/GameStandard';
import { BannerNavigation, useAutoPlay } from 'shared/banners/heroBanner';
import { formatDateTime } from 'utils/dateUtils';
import { BannerLayout } from 'shared/banners/bannerLayout';
import PublisherDynamic from 'shared/banners/dynamic/overlayContent/PublisherDynamic';

interface PublisherData {
  publisherName?: string;
  publisherLogoUrl?: string;
  publisherLogoText?: string;
  isVerified?: boolean;
  stats?: {
    followers?: number;
    games?: number;
    upcomingEvents?: number;
  };
}

interface GameData {
  gameLogoUrl?: string;
  gameTitle?: string;
  stats?: {
    playCount?: number;
    followers?: number;
    likes?: number;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

interface VideoData {
  gameLogoUrl?: string;
  gameTitle?: string;
  publisherInfo?: {
    imageUrl?: string;
    publisherName?: string;
    isVerified?: boolean;
  };
  stats?: {
    uploadTime?: string;
    views?: string;
    likes?: string;
    playCount?: number;
    followers?: number;
  };
  onPlayClick?: () => void;
  onSaveClick?: () => void;
}

interface EventData {
  gameLogoUrl?: string;
  eventTitle?: string;
  stats?: {
    dropDate?: string;
    expires?: string;
    saveCount: number;
    playCount: number;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

interface BannerItemData {
  id: string;
  media_url: string;
  start_date: string;
  end_date: string | null;
  title: string;
  is_saved_event?: boolean;
  is_saved_offer?: boolean;
}

type DynamicContentData = PublisherData | GameData | VideoData | EventData;

interface BannerData {
  id: string;
  title: string;
  media_url: string;
  media_type: string;
  game_slug: string;
  game_id: string;
  trailer_banner: BannerItemData[];
  offer_banner: BannerItemData[];
  event_banner: BannerItemData[];
  modified_by: string;
  created_at: string;
  display_order: number;
  // Additional properties for component data
  gameLogoUrl?: string;
  gameTitle?: string;
  contentData?: DynamicContentData;
  // For backward compatibility with existing data structure
  imageUrl?: string;
  duration?: string; // Duration in seconds for video content
}

interface StandardBannerProps {
  showNavigation?: boolean;
  bannerType?: BannerType;
  backgroundImage?: string;
  focused?: boolean;
  data?: BannerData[];
  /** Auto-play interval in milliseconds (default: 4000) */
  autoPlayInterval?: number;
  /** Whether to enable auto-play (default: true) */
  autoPlay?: boolean;
  /** Callback when slide changes */
  onSlideChange?: (currentIndex: number, item: BannerData) => void;
  /** Callback when banner indicator is focused */
  onIndicatorFocus?: () => void;
}

const StandardBanner: React.FC<StandardBannerProps> = ({
  showNavigation = true,
  bannerType = BannerType.STANDARD,
  backgroundImage,
  focused = false,
  autoPlayInterval = 15000,
  autoPlay = true,
  onSlideChange,
  onIndicatorFocus,
  data = [],
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const { ref, focusKey } = useFocusable({
    focusable: true,
    saveLastFocusedChild: true,
    trackChildren: true,
    autoRestoreFocus: true,
    focusKey: 'standard-banner',
    preferredChildFocusKey: 'nav-dot-0', // Focus first image indicator
  });

  /**
   * Moves to the next slide
   */
  const nextSlide = useCallback(() => {
    if (data.length === 0) return;

    const newIndex = (currentSlide + 1) % data.length;
    setCurrentSlide(newIndex);

    if (onSlideChange) {
      onSlideChange(newIndex, data[newIndex]);
    }
  }, [currentSlide, data, onSlideChange]);

  /**
   * Jumps to a specific slide
   */
  const handleSlideSelect = useCallback(
    (index: number) => {
      if (index >= 0 && index < data.length) {
        setCurrentSlide(index);

        if (onSlideChange) {
          onSlideChange(index, data[index]);
        }
      }
    },
    [data, onSlideChange]
  );

  // Auto-play functionality
  const { startAutoPlay, stopAutoPlay } = useAutoPlay({
    autoPlay,
    dataLength: data.length,
    autoPlayInterval,
    nextSlide,
  });

  /**
   * Effect to reset slide when data changes
   */
  useEffect(() => {
    if (data.length > 0 && currentSlide >= data.length) {
      setCurrentSlide(0);
    }
  }, [data.length, currentSlide]);

  const handleImageIndicatorFocus = useCallback(() => {
    if (onIndicatorFocus) {
      // Use custom focus handler if provided
      onIndicatorFocus();
    } else {
      // Default behavior: scroll window to top
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [onIndicatorFocus]);

  const renderDynamicContent = (slideData: BannerData) => {
    const contentType = slideData.media_type as ContentType;
    const data = slideData.contentData || {};
    const bannerData = {
      event_banner: slideData.event_banner,
      offer_banner: slideData.offer_banner,
    };

    switch (contentType) {
      case ContentType.PUBLISHER:
        return (
          <BannerOverlayContent bottomLeft={<PublisherDynamic {...(data as PublisherData)} />} />
        );

      case ContentType.GAME:
      case ContentType.IMAGE:
        return (
          <BannerOverlayContent
            bottomLeft={
              <GameStandard gameLogoUrl={slideData.gameLogoUrl || ''} {...(data as GameData)} />
            }
          />
        );
      case ContentType.VIDEO:
      case ContentType.TRAILER:
        return (
          <DynamicVideoOverlay
            topLeftText={contentType === ContentType.VIDEO ? 'New Video' : 'New Trailer'}
            videoData={{
              gameLogoUrl: slideData.gameLogoUrl || '',
              ...(data as VideoData),
              stats: {
                ...(data as VideoData).stats,
                uploadTime: slideData.created_at || (data as VideoData).stats?.uploadTime,
              },
            }}
            bottomRightText={formatDateTime(slideData?.duration ?? '')}
          />
        );
      case ContentType.EVENT: {
        const eventBannerData = bannerData?.event_banner?.[0];
        return (
          <DynamicPromoOverlay
            topLeftText="New Event"
            eventData={{
              gameLogoUrl: slideData.gameLogoUrl || '',
              eventTitle:
                eventBannerData?.title || (data as EventData)?.eventTitle || slideData.title,
              stats: {
                dropDate: eventBannerData?.start_date
                  ? formatDateTime(eventBannerData.start_date)
                  : (data as EventData)?.stats?.dropDate,
                expires: eventBannerData?.end_date
                  ? formatDateTime(eventBannerData.end_date)
                  : (data as EventData)?.stats?.expires,
                playCount: (data as EventData)?.stats?.playCount ?? 0,
                saveCount: (data as EventData)?.stats?.saveCount ?? 0,
              },
              onMoreInfoClick: (data as EventData)?.onMoreInfoClick,
              onSaveClick: (data as EventData)?.onSaveClick,
            }}
          />
        );
      }
      case ContentType.OFFER: {
        const offerBannerData = bannerData?.offer_banner?.[0];
        return (
          <DynamicPromoOverlay
            topLeftText="New Offer"
            eventData={{
              gameLogoUrl: slideData.gameLogoUrl || '',
              eventTitle:
                offerBannerData?.title || (data as EventData)?.eventTitle || slideData.title,
              stats: {
                dropDate: offerBannerData?.start_date
                  ? formatDateTime(offerBannerData.start_date)
                  : (data as EventData)?.stats?.dropDate,
                expires: offerBannerData?.end_date
                  ? formatDateTime(offerBannerData.end_date)
                  : (data as EventData)?.stats?.expires,
                playCount: (data as EventData)?.stats?.playCount ?? 0,
                saveCount: (data as EventData)?.stats?.saveCount ?? 0,
              },
              onMoreInfoClick: (data as EventData)?.onMoreInfoClick,
              onSaveClick: (data as EventData)?.onSaveClick,
            }}
          />
        );
      }
      default:
        return (
          <BannerOverlayContent
            bottomLeft={
              <GameStandard gameLogoUrl={slideData.gameLogoUrl || ''} {...(data as GameData)} />
            }
          />
        );
    }
  };

  const currentSlideData = data[currentSlide];

  return (
    <FocusContext.Provider value={focusKey}>
      <div ref={ref} onMouseEnter={stopAutoPlay} onMouseLeave={startAutoPlay}>
        <BannerLayout
          bannerType={bannerType}
          mediaUrl={currentSlideData?.media_url || currentSlideData?.imageUrl || backgroundImage}
          mediaType={(currentSlideData?.media_type as GAME_MEDIA) || GAME_MEDIA.IMAGE}
          focused={focused}
        >
          {currentSlideData && renderDynamicContent(currentSlideData)}
          {showNavigation && data.length > 1 && (
            <BannerNavigation
              data={data.map(item => ({ imageUrl: item.media_url || item.imageUrl }))}
              bannerType={bannerType}
              currentSlide={currentSlide}
              onSlideSelect={handleSlideSelect}
              onFocus={handleImageIndicatorFocus}
            />
          )}
        </BannerLayout>
      </div>
    </FocusContext.Provider>
  );
};

export default StandardBanner;
