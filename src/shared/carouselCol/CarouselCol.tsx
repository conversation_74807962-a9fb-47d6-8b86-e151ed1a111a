import {
  FocusContext,
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
} from '@noriginmedia/norigin-spatial-navigation';
import React, { useCallback, useRef, useState, useEffect } from 'react';
import { Paginated } from 'types/api/paginated.api';
import {
  ContentColumnScrollingContent,
  ContentColumnScrollingWrapper,
  ContentColumnWrapper,
} from './style';
import SkeletonLoader, { SkeletonSizeType } from 'shared/skeletonLoader';
import { Col } from 'styles/sharedStyles';
import { BodyXL } from 'styles/theme/typography';

/**
 * Props interface for the CarouselColumn component
 * @template T - The type of items in the carousel
 */
interface IContentColProps<T> {
  /** The title displayed above the carousel */
  title?: string;
  /** Static items to display (optional, used when not using API) */
  items?: T[];
  /** Function to render each item in the carousel */
  renderItem: (item: T, index: number) => React.ReactNode;
  /** Callback when the carousel column gains focus */
  onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  /** Optional API function to fetch paginated data */
  handleApiCall?: (page: number) => Promise<Paginated<T>>;
  /** Whether to load initial data on mount (default: true) */
  initialLoad?: boolean;
  /** Trigger to refetch data - when this changes to true, data will be refetched */
  refetchTrigger?: boolean;
  /** Callback when refetch is completed */
  onRefetchComplete?: () => void;
  gap?: string;
  skeletonVariant?: SkeletonSizeType;
  /** Custom skeleton loader width (overrides variant) */
  skeletonWidth?: string;
  /** Custom skeleton loader height (overrides variant) */
  skeletonHeight?: string;
  skeletonGap?: string;
  /** Custom skeleton loader border radius (overrides variant) */
  skeletonBorderRadius?: string;
  /** Number of skeleton loaders to show when loading (default: 5) */
  skeletonCount?: number;
  isSkeletonCol?: boolean;
  noDataMessage?: string;
}

/**
 * CarouselColumn Component
 *
 * A vertically scrollable carousel component with focus-based navigation and lazy loading.
 * Supports both static items and dynamic API-driven content with intelligent pagination.
 *
 * Features:
 * - Focus-based navigation using spatial navigation
 * - Lazy loading: API calls triggered when user focuses on 3rd last item
 * - Smart pagination: Uses total count from API to prevent unnecessary requests
 * - Smooth vertical scrolling
 * - Loading states and error handling
 *
 * @template T - The type of items displayed in the carousel
 *
 * @example
 * // Static items
 * <CarouselColumn
 *   title="My Games"
 *   items={gamesList}
 *   renderItem={(game, index) => <GameCard key={index} game={game} />}
 *   onFocus={handleColumnFocus}
 * />
 *
 * @example
 * // API-driven with lazy loading
 * <CarouselColumn
 *   title="Saved Videos"
 *   handleApiCall={(page) => fetchSavedVideos(page, 10)}
 *   renderItem={(video, index) => <VideoCard key={index} video={video} />}
 *   onFocus={handleColumnFocus}
 * />
 */
const CarouselCol = <T,>({
  title: columnTitle,
  items: initialItems = [],
  renderItem,
  onFocus,
  handleApiCall,
  initialLoad = true,
  refetchTrigger = false,
  onRefetchComplete,
  gap,
  skeletonBorderRadius,
  skeletonCount,
  skeletonHeight,
  skeletonVariant,
  skeletonWidth,
  skeletonGap,
  isSkeletonCol,
  noDataMessage,
}: IContentColProps<T>) => {
  // State management for API-driven data
  /** Current items displayed in the carousel */
  const [items, setItems] = useState<T[]>(initialItems);
  /** Loading state to prevent duplicate API calls */
  const [loading, setLoading] = useState(false);
  /** Current page number for pagination */
  const [currentPage, setCurrentPage] = useState(1);
  /** Whether there are more pages to load */
  const [hasNextPage, setHasNextPage] = useState(true);
  /** Total count of items available from the API */
  const [totalCount, setTotalCount] = useState(0);
  /** Flag to prevent further API calls after initial failure */
  const [shouldStopFetching, setShouldStopFetching] = useState(false);

  const { ref, focusKey } = useFocusable({
    onFocus,
    saveLastFocusedChild: true,
    trackChildren: true,
    preferredChildFocusKey: `${columnTitle}-0`,
    autoRestoreFocus: true,
    // isFocusBoundary: false,
    // onArrowPress: direction => {
    //   // Allow up arrow to escape to parent when on first item
    //   if (direction === 'up') {
    //     return false; // Don't handle, let parent handle
    //   }
    //   return true; // Handle other directions normally
    // },
  });

  const scrollingRef = useRef<HTMLDivElement>(null);

  /**
   * Loads data from the API
   * @param page - Page number to load
   * @param append - Whether to append to existing items or replace them
   */
  const loadData = useCallback(
    async (page: number, append: boolean = false) => {
      if (!handleApiCall || loading || shouldStopFetching) return;

      try {
        setLoading(true);

        const response = await handleApiCall(page);

        // Check if response is valid and has data
        if (!response || !response.data || response?.data?.length === 0) {
          // No data returned - stop further fetching
          setShouldStopFetching(true);
          setHasNextPage(false);
          if (!append) {
            setItems([]);
          }
          return;
        }

        if (append) {
          setItems(prevItems => [...prevItems, ...response.data]);
        } else {
          setItems(response.data);
        }

        setTotalCount(response.count || 0);
        setCurrentPage(page);

        // Check if there's more data to fetch by comparing current items with total count
        const currentItemsCount = append
          ? items?.length + response.data.length
          : response.data.length;
        setHasNextPage(currentItemsCount < (response.count || 0));
      } catch (error) {
        console.error(`Error loading data for ${columnTitle}:`, error);

        setShouldStopFetching(true);
        setHasNextPage(false);

        // If this is the initial load and it fails, set empty items
        if (!append) {
          setItems([]);
        }
      } finally {
        setLoading(false);
      }
    },
    [handleApiCall, loading, columnTitle, shouldStopFetching, items?.length]
  );

  /**
   * Effect to handle refetch trigger
   * When refetchTrigger becomes true, refetch data and call onRefetchComplete
   */
  useEffect(() => {
    if (refetchTrigger && handleApiCall) {
      // Reset state and refetch data
      setCurrentPage(1);
      setHasNextPage(true);
      setShouldStopFetching(false);
      setTotalCount(0);
      setItems([]);

      // Load fresh data
      loadData(1, false).finally(() => {
        // Call completion callback to reset the trigger
        if (onRefetchComplete) {
          onRefetchComplete();
        }
      });
    }
  }, [refetchTrigger, handleApiCall, loadData, onRefetchComplete]);

  /**
   * Effect to load initial data when component mounts
   * Only runs if handleApiCall is provided and initialLoad is true
   */
  useEffect(() => {
    if (handleApiCall && initialLoad && items?.length === 0 && !shouldStopFetching) {
      loadData(1);
    }
  }, [handleApiCall, initialLoad, loadData, items?.length, shouldStopFetching]);

  /**
   * Loads the next page of data
   * Only executes if there are more pages and not currently loading
   */
  const loadNextPage = useCallback(() => {
    if (hasNextPage && !loading && !shouldStopFetching) {
      loadData(currentPage + 1, true);
    }
  }, [hasNextPage, loading, currentPage, loadData, shouldStopFetching]);

  /**
   * Handles focus events on individual carousel items
   * Implements lazy loading by triggering API calls when user approaches the end
   *
   * @param y - Vertical scroll position for the focused item
   * @param index - Index of the focused item
   */
  const onItemFocus = useCallback(
    ({ y }: { y: number }, index: number) => {
      // Smooth scroll to keep focused item visible
      scrollingRef.current?.scrollTo({
        top: y,
        behavior: 'smooth',
      });

      // Lazy loading logic: Load next page when user focuses on 3rd last item
      // This provides a smooth experience by loading data before user reaches the end
      const isNearEnd = index >= items.length - 3;
      const hasMoreData = items.length < totalCount;

      // Only trigger API call if:
      // 1. User is near the end (3rd last item)
      // 2. There's more data available on the server
      // 3. Not currently loading
      // 4. API handler is available
      // 5. Not in error state and should continue fetching
      if (isNearEnd && hasMoreData && !loading && handleApiCall && !shouldStopFetching) {
        loadNextPage();
      }
    },
    [scrollingRef, items?.length, totalCount, loading, handleApiCall, loadNextPage] // eslint-disable-line react-hooks/exhaustive-deps
  );

  // Don't render the column if there's no data and not loading
  // Also don't render if there was an error or we should stop fetching
  if ((!items || items.length === 0) && !loading) {
    return (
      <Col align="center" justify="center" height="100%">
        <BodyXL>{noDataMessage ?? 'No data'}</BodyXL>
      </Col>
    );
  }

  return (
    <FocusContext.Provider value={focusKey}>
      <ContentColumnWrapper ref={ref}>
        <ContentColumnScrollingWrapper ref={scrollingRef}>
          <ContentColumnScrollingContent gap={gap}>
            {items?.map((item, index) => {
              // Clone the rendered item and inject focus handling and metadata
              const renderedItem = renderItem(item, index);
              if (React.isValidElement(renderedItem)) {
                return React.cloneElement(renderedItem, {
                  key: `${columnTitle}-${index}`,
                  focusKey: `${columnTitle}-${index}`, // Pass the expected focus key
                  onFocus: (focusDetails: { y: number }) => onItemFocus(focusDetails, index),
                  totalItems: items.length,
                  index, // Ensure index is passed
                } as unknown as React.HTMLAttributes<HTMLElement>);
              }
              return (
                <React.Fragment key={`${columnTitle}-${index}`}>{renderedItem}</React.Fragment>
              );
            })}
            {/* Loading indicator shown during API calls */}
            {loading && (
              <SkeletonLoader
                key="skeleton-loading"
                variant={skeletonVariant}
                width={skeletonWidth}
                height={skeletonHeight}
                borderRadius={skeletonBorderRadius}
                count={skeletonCount}
                gap={skeletonGap}
                isColumn={isSkeletonCol}
              />
            )}
          </ContentColumnScrollingContent>
        </ContentColumnScrollingWrapper>
      </ContentColumnWrapper>
    </FocusContext.Provider>
  );
};

export default CarouselCol;
