import styled from 'styled-components';

/**
 * Main wrapper for the entire carousel column component
 * Sets up the vertical layout structure
 */
export const ContentColumnWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 30px;
  min-height: 0; /* Allow flex shrinking */
`;

/**
 * Scrollable wrapper that contains the column content
 * Handles vertical overflow and scrolling
 */
export const ContentColumnScrollingWrapper = styled.div`
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;

  /* Hide scrollbar for Firefox */
  scrollbar-width: none;

  /* Hide scrollbar for WebKit browsers (Chrome, Safari, Edge) */
  &::-webkit-scrollbar {
    display: none;
  }

  scroll-behavior: smooth;
`;

/**
 * Content container that holds all the carousel items
 * Uses flexbox for vertical layout of items
 */

interface IContentColumnScrollingContent {
  gap?: string;
}

export const ContentColumnScrollingContent = styled.div<IContentColumnScrollingContent>`
  display: flex;
  flex-direction: column;
  gap: ${props => props?.gap ?? '12px'}; /* Space between items */
  min-height: min-content;
  padding-bottom: 20px; /* Extra space at bottom for better UX */

  /* Ensure items maintain their aspect ratio */
  > * {
    flex-shrink: 0;
  }
`;
