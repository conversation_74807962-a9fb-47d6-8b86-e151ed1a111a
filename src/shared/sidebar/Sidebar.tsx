import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { homePath, libraryPagePath, profileRoute } from 'routes/path';
import { MenuItem } from 'shared/sidebar/components/menuItem/MenuItem';
import { MenuWrapper } from './style';

// Import sidebar icons
import ProfileIcon from 'assets/icons/profile.svg';
import FollowingIcon from 'assets/icons/sidebarIcons/Following';
import FriendsIcon from 'assets/icons/sidebarIcons/Friends';
import HomeIcon from 'assets/icons/sidebarIcons/Home';
import LibraryIcon from 'assets/icons/sidebarIcons/Library';
import SearchIcon from 'assets/icons/sidebarIcons/Search';
import SettingsIcon from 'assets/icons/sidebarIcons/Settings';
import { useCallback, useContext, useMemo } from 'react';
import { FollowerList } from 'shared/follower-list';
import { FriendList } from 'shared/friend-list';
import { ModalContext } from 'state-management/context/ModalContext';

interface IMenuProps {
  focusKey: string;
  currentPage: string;
  onNavigate: (page: string, path: string) => void;
}

export const Sidebar: React.FC<IMenuProps> = ({
  focusKey: focusKeyParam,
  currentPage,
  onNavigate,
}) => {
  const { showModal } = useContext(ModalContext);

  // Memoize menu items so they don’t get recreated on every render
  const menuItems = useMemo(
    () => [
      {
        title: 'Profile',
        page: 'profile',
        icon: ProfileIcon,
        path: profileRoute,
      },
      {
        title: 'Home',
        page: 'home',
        icon: HomeIcon,
        path: homePath,
      },
      {
        title: 'Search',
        page: 'search',
        icon: SearchIcon,
        path: `${profileRoute}/123`,
      },
      {
        title: 'Library',
        page: 'library',
        icon: LibraryIcon,
        path: libraryPagePath,
      },
      {
        title: 'People',
        page: 'people',
        icon: FriendsIcon,
        onClick: () => {
          showModal(<FriendList isModal />);
        },
      },
      {
        title: 'Following',
        page: 'following',
        icon: FollowingIcon,
        onClick: () => {
          showModal(<FollowerList isModal />);
        },
      },
      {
        title: 'Settings',
        page: 'settings',
        icon: SettingsIcon,
        path: `${profileRoute}/123`,
      },
      // {
      //   title: 'Components',
      //   page: 'components',
      //   icon: SettingsIcon,
      //   path: componentsPath,
      // },
    ],
    [showModal]
  );

  const { ref, hasFocusedChild, focusKey } = useFocusable({
    focusable: true,
    saveLastFocusedChild: false,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
    focusKey: focusKeyParam,
    preferredChildFocusKey: `MENU_ITEM_${currentPage.toUpperCase()}`,
    onEnterPress: () => {},
    onEnterRelease: () => {},
    onArrowPress: direction => {
      // Allow right arrow to escape to main content
      if (direction === 'right') {
        return false; // Don't handle, let spatial navigation find next focusable element
      }
      return true; // Handle other directions normally
    },
    onArrowRelease: () => {},
    onFocus: () => {},
    onBlur: () => {},
    extraProps: { foo: 'bar' },
  });

  // Removed automatic focus on mount to allow content to receive initial focus
  // useEffect(() => {
  //   focusSelf();
  // }, [focusSelf]);

  // Menu is expanded when any child has focus
  const isExpanded = hasFocusedChild;

  // Stable callback for menu item selection
  const handleSelect = useCallback(
    (item: (typeof menuItems)[number]) => {
      if (item.onClick) {
        item.onClick();
      } else if (item.path) {
        onNavigate(item.page, item.path);
      }
    },
    [onNavigate]
  );

  return (
    <FocusContext.Provider value={focusKey}>
      <MenuWrapper ref={ref} $hasFocusedChild={hasFocusedChild} $expanded={isExpanded}>
        {menuItems.map(item => (
          <MenuItem
            key={item.title}
            title={item.title}
            expanded={isExpanded}
            isActive={currentPage === item.page}
            icon={item.icon}
            focusKey={`MENU_ITEM_${item.page.toUpperCase()}`}
            onSelect={() => handleSelect(item)}
          />
        ))}
      </MenuWrapper>
    </FocusContext.Provider>
  );
};
