import styled from 'styled-components';

interface MenuWrapperProps {
  $hasFocusedChild: boolean;
  $expanded: boolean;
}

export const MenuWrapper = styled.aside<MenuWrapperProps>`
  flex: 1;
  max-width: ${({ $expanded }) => ($expanded ? '281px' : '96px')};
  min-width: ${({ $expanded }) => ($expanded ? '281px' : '96px')};
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: ${({ $hasFocusedChild, theme }) =>
    $hasFocusedChild ? theme.bgColor : theme.bgColor};
  padding-top: 40px;
  gap: 40px;
  transition:
    max-width 0.3s ease,
    min-width 0.3s ease,
    padding 0.3s ease;
  padding-inline: 24px;
`;
