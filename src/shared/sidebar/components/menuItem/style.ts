import styled, { css } from 'styled-components';
import { HeadingSM } from 'styles/theme/typography';

interface MenuItemBoxProps {
  $focused: boolean;
  $expanded: boolean;
  $isActive: boolean;
}

export const MenuItemBox = styled.div<MenuItemBoxProps>`
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
  border-color: ${({ theme, $focused }) =>
    $focused ? theme.outlineStrokeFocusWhite : 'transparent'};

  border-style: solid;
  border-width: 2px;
  box-sizing: border-box;
  transition:
    width 0.3s ease,
    background-color 0.3s ease,
    gap 0.3s ease;
  overflow: hidden;
  position: relative;

  ${props =>
    !props?.$expanded
      ? css`
          justify-content: center;
          padding-block: 16px;
        `
      : css`
          padding: 16px;
          border-radius: 4px;
        `}
`;

export const MenuItemIconContainer = styled.div<{ $expanded: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  transition: all 0.3s ease;

  /* Ensure both SVG and img elements are properly aligned */
  svg,
  img {
    display: block;
    width: 24px;
    height: 24px;
    object-fit: contain;
  }

  /* Add transition for SVG fill color changes */
  svg {
    transition: fill 0.3s ease;
  }
`;

export const MenuItemTitle = styled(HeadingSM)<MenuItemBoxProps>`
  color: ${({ $isActive, $focused, theme }) =>
    $isActive || $focused ? theme.outlineStrokeFocusTertiary : theme.iconWhite};
  letter-spacing: 0%;
  opacity: ${({ $expanded }) => ($expanded ? 1 : 0)};
  transition:
    opacity 0.3s ease,
    color 0.3s ease;
  white-space: nowrap;
`;

export const MenuItemIcon = styled.img<{ $expanded: boolean }>`
  width: 24px;
  height: 24px;
  display: block;
  object-fit: contain;
  flex-shrink: 0;
  transition: all 0.3s ease;
`;
