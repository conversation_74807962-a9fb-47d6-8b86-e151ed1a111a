import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';
import { useTheme } from 'styled-components';
import { MenuItemBox, MenuItemTitle, MenuItemIconContainer } from './style';

interface IIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

interface IMenuItemProps {
  title: string;
  expanded: boolean;
  isActive: boolean;
  icon: React.ComponentType<IIconProps> | string | null;
  onSelect: () => void;
  focusKey: string;
}

export function MenuItem({ title, expanded, isActive, icon, onSelect, focusKey }: IMenuItemProps) {
  const theme = useTheme();
  const { ref, focused } = useFocusable({
    focusKey,
    onEnterPress: () => {
      onSelect();
    },
  });

  const getIconColor = () => {
    // Use the same color as focused text for both active and focused states
    if (isActive || focused) return theme.outlineStrokeFocusTertiary;
    return theme.textPrimary;
  };

  const renderIcon = () => {
    if (!icon) return null;

    // If it's a React component (TSX icon)
    if (typeof icon === 'function') {
      const IconComponent = icon as React.ComponentType<IIconProps>;
      return (
        <MenuItemIconContainer $expanded={expanded}>
          <IconComponent width={40} height={40} fill={getIconColor()} />
        </MenuItemIconContainer>
      );
    }

    // If it's a string (SVG path) - fallback for profile icon
    if (typeof icon === 'string') {
      return (
        <MenuItemIconContainer $expanded={expanded}>
          <img src={icon} alt={title} width={40} height={40} />
        </MenuItemIconContainer>
      );
    }

    return null;
  };

  return (
    <MenuItemBox
      ref={ref}
      $focused={focused}
      $expanded={expanded}
      $isActive={isActive}
      onClick={onSelect}
    >
      {renderIcon()}
      {expanded && (
        <MenuItemTitle $expanded={expanded} $focused={focused} $isActive={isActive}>
          {title}
        </MenuItemTitle>
      )}
    </MenuItemBox>
  );
}
