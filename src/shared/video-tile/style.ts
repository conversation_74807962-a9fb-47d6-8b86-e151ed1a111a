import styled from 'styled-components';
import { BodyMD, LabelMD, LabelSM } from 'styles/theme/typography';
interface IVideoTileWrapperProps {
  $focused: boolean;
}

export const VideoTileWrapper = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 412px;
`;

const Badge = styled(LabelSM)`
  position: absolute;
  top: 12px;
  background-color: ${prop => prop.theme.surfaceSecondaryOpacity40};
  color: ${prop => prop.theme.surfaceWhite};
  padding: 4px 10px;
  backdrop-filter: blur(4px);
  border-radius: 4px;
  border: 1px solid ${prop => prop.theme.textPrimary};
  z-index: 2;
`;

export const TimeAgoBadge = styled(Badge)`
  left: 12px;
`;

export const DurationBadge = styled(Badge)`
  right: 12px;
`;

export const VideoTitle = styled(BodyMD)`
  margin: 0;
  text-align: start;
  z-index: 2;
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
`;

export const ChannelInfo = styled.div`
  display: flex;
  align-items: center;
  height: 48px;
  width: 412px; /* Slightly inset as requested */
  gap: 12px;
`;

export const ChannelLogo = styled.img`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: ${prop => prop.theme.bgColor};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  flex-shrink: 0;
  object-fit: cover;
`;
export const MediaContainer = styled.div<IVideoTileWrapperProps>`
  position: relative;
  width: 100%;
  height: 232px;
  border: 4px solid ${({ $focused, theme }) => ($focused ? theme.textPrimary : 'transparent')};
`;
export const ChannelName = styled(LabelMD)``;

export const VerifiedIcon = styled.img``;
export const StyledVideo = styled.video`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* Ensures the video covers the container, just like the thumbnail */
  z-index: 0; /* Places the video behind overlays and badges */
`;
