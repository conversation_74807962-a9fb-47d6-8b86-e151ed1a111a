import {
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
  type KeyPressDetails,
} from '@noriginmedia/norigin-spatial-navigation';
import { formatDuration, formatTimeAgo, handleLogoError } from 'utils/helpers.utils';

import {
  ChannelInfo,
  ChannelLogo,
  ChannelName,
  DurationBadge,
  MediaContainer, // Import the new container
  TimeAgoBadge,
  VerifiedIcon,
  VideoTileWrapper,
  VideoTitle,
} from './style';
import verifiedIcon from 'assets/icons/verified-icon.svg';
import { ImageVideo } from 'shared/imageVideo';
import { CardOverlay } from 'shared/card-overlay/CardOverlay';

// --- Interface remains the same ---
interface IVideoTileProps {
  title: string;
  thumbnailUrl: string;
  videoUrl?: string;
  channelName: string;
  channelLogo: string;
  timeAgo: string;
  duration: number;
  isVerified?: boolean;
  focusKey?: string;
  onEnterPress?: (props: object, details: KeyPressDetails) => void;
  onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  onCardFocused?: (props: object) => void;
}

export function VideoTile({
  title,
  thumbnailUrl,
  videoUrl,
  channelName,
  channelLogo,
  timeAgo,
  duration,
  isVerified = false,
  focusKey,
  onEnterPress,
  onFocus,
  onCardFocused,
}: IVideoTileProps) {
  const { ref, focused } = useFocusable({
    focusKey,
    onEnterPress,
    onFocus: (layout, props, details) => {
      onFocus(layout, props, details);
      if (onCardFocused) {
        onCardFocused(props);
      }
    },
    extraProps: { title, channelName, videoUrl, timeAgo, duration, isVerified, thumbnailUrl },
  });

  return (
    // The ref and focused state are now on the main wrapper
    <VideoTileWrapper ref={ref}>
      <MediaContainer $focused={focused}>
        {/* Replace CardBox and StyledVideo with the ImageVideo component */}
        <ImageVideo
          imageUrl={thumbnailUrl}
          videoUrl={videoUrl}
          isFocused={focused}
          width="100%"
          height="100%"
          // delay={500} // Optional: you can configure the play delay
        />

        {/* Overlays and badges are placed on top */}
        <CardOverlay borderRadius="0" focused={focused} />
        <TimeAgoBadge>{formatTimeAgo(timeAgo)}</TimeAgoBadge>
        <DurationBadge>{formatDuration(duration)}</DurationBadge>
        <VideoTitle>{title}</VideoTitle>
      </MediaContainer>

      {/* Channel info section remains unchanged */}
      <ChannelInfo>
        {channelLogo && <ChannelLogo onError={handleLogoError} src={channelLogo} />}
        <ChannelName>{channelName}</ChannelName>
        {isVerified && <VerifiedIcon src={verifiedIcon} alt="verified-icon" />}
      </ChannelInfo>
    </VideoTileWrapper>
  );
}
