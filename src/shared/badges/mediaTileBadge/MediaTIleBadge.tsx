import React from 'react';
import { LabelMD } from 'styles/theme/typography';
import { MediaTileBadgeContainer } from './style';

interface MediaTileBadgeProps {
  children?: string;
  showBorder?: boolean;
}

const MediaTileBadge: React.FC<MediaTileBadgeProps> = ({ children, showBorder = true }) => {
  return (
    <MediaTileBadgeContainer showBorder={showBorder}>
      <LabelMD>{children}</LabelMD>
    </MediaTileBadgeContainer>
  );
};

export default MediaTileBadge;
