import styled from 'styled-components';

interface MediaTileBadgeContainerProps {
  showBorder?: boolean;
}

export const MediaTileBadgeContainer = styled.div<MediaTileBadgeContainerProps>`
  background: ${({ theme }) => theme.surfaceSecondaryOpacity60};
  padding: 8px 24px;
  border-radius: 4px;
  border: ${({ showBorder, theme }) =>
    showBorder ? `1px solid ${theme.outlineStrokeFocusTertiary}` : 'none'};
`;
