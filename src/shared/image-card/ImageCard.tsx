import {
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
  type KeyPressDetails,
} from '@noriginmedia/norigin-spatial-navigation';
import styled from 'styled-components';
import { CardBox } from 'shared/card-box/CardBox';
import { CardOverlay } from 'shared/card-overlay/CardOverlay';

// A simple wrapper to hold the focusable ref
const CardWrapper = styled.div`
  border-radius: 8px;
`;

interface ImageCardProps {
  imageUrl: string;
  altText?: string;
  focusKey?: string; // Accept focus key from parent
  onEnterPress?: (props: object, details: KeyPressDetails) => void;
  onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
}

export function ImageCard({
  imageUrl,
  altText = 'image',
  focusKey, // Get the focus key from parent
  onEnterPress,
  onFocus,
}: ImageCardProps) {
  const { ref, focused } = useFocusable({
    focusKey, // Use the provided focus key
    onEnterPress,
    onFocus,
    extraProps: { imageUrl, altText },
  });

  return (
    <CardWrapper ref={ref}>
      <CardBox focused={focused} imageUrl={imageUrl} width="412px" height="232px" borderRadius="0">
        <CardOverlay borderRadius="0" focused={focused}></CardOverlay>
      </CardBox>
    </CardWrapper>
  );
}
