import styled, { keyframes } from 'styled-components';

// Shimmer animation for the skeleton loader
const shimmer = keyframes`
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
`;

interface SkeletonContainerProps {
  $width: string;
  $height: string;
  $borderRadius: string;
}

export const SkeletonContainer = styled.div<SkeletonContainerProps>`
  width: ${props => props.$width};
  height: ${props => props.$height};
  border-radius: ${props => props.$borderRadius};
  background: ${props => props.theme.surfaceSecondaryOpacity100 || '#313544'};
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
`;

export const SkeletonShimmer = styled.div`
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    ${props => props.theme.surfaceSecondaryOpacity40 || 'rgba(255, 255, 255, 0.1)'},
    transparent
  );
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: ${shimmer} 1.5s infinite;
`;
