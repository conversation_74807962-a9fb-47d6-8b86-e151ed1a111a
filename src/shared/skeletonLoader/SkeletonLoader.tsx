import React from 'react';
import { SkeletonContainer, SkeletonShimmer } from './style';
import { SKELETON_SIZES, type SkeletonSizeType } from './constants';

interface SkeletonLoaderProps {
  /** Predefined size type for common card components */
  variant?: SkeletonSizeType;
  /** Custom width of the skeleton loader (overrides variant) */
  width?: string;
  /** Custom height of the skeleton loader (overrides variant) */
  height?: string;
  /** Custom border radius to match the card design (overrides variant) */
  borderRadius?: string;
  /** Number of skeleton items to show */
  count?: number;
  /** Gap between skeleton items when count > 1 */
  gap?: string;
  isColumn?: boolean;
}

/**
 * SkeletonLoader Component
 *
 * A flexible skeleton loader that matches card dimensions and provides
 * a smooth loading animation while content is being fetched.
 *
 * @example
 * // Using predefined variants
 * <SkeletonLoader variant="GAME_TILE_VERTICAL" count={5} />
 * <SkeletonLoader variant="GAME_TILE_HORIZONTAL" count={3} />
 * <SkeletonLoader variant="PROMO_CARD" count={2} />
 * <SkeletonLoader variant="VIDEO_TILE" count={4} />
 * <SkeletonLoader variant="CATEGORY_CARD" count={6} />
 * <SkeletonLoader variant="IMAGE_CARD" count={4} />
 *
 * @example
 * // Using custom dimensions
 * <SkeletonLoader width="300px" height="200px" borderRadius="8px" />
 */
export const SkeletonLoader: React.FC<SkeletonLoaderProps> = ({
  variant,
  width,
  height,
  borderRadius,
  count = 1,
  gap = '32px',
  isColumn,
}) => {
  // Get dimensions from variant or use custom values
  const dimensions = variant ? SKELETON_SIZES[variant] : null;
  const finalWidth = width || dimensions?.width || '264px';
  const finalHeight = height || dimensions?.height || '330px';
  const finalBorderRadius = borderRadius || dimensions?.borderRadius || '0px';

  // Create array of skeleton items based on count
  const skeletonItems = Array.from({ length: count }, (_, index) => (
    <SkeletonContainer
      key={`skeleton-${index}`}
      $width={finalWidth}
      $height={finalHeight}
      $borderRadius={finalBorderRadius}
    >
      <SkeletonShimmer />
    </SkeletonContainer>
  ));

  // If count is 1, return single skeleton
  if (count === 1) {
    return skeletonItems[0];
  }

  // If count > 1, return wrapped in flex container
  return (
    <div
      style={{
        display: 'flex',
        gap,
        alignItems: 'center',
        flexDirection: isColumn ? 'column' : 'row',
      }}
    >
      {skeletonItems}
    </div>
  );
};

export default SkeletonLoader;
