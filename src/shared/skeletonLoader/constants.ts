// Predefined skeleton sizes for different card types
export const SKELETON_SIZES = {
  GAME_TILE_VERTICAL: { width: '264px', height: '330px', borderRadius: '0px' },
  GAME_TILE_HORIZONTAL: { width: '412px', height: '240px', borderRadius: '0px' },
  PROMO_CARD: { width: '544px', height: '216px', borderRadius: '4px' },
  VIDEO_TILE: { width: '412px', height: '232px', borderRadius: '0px' },
  CATEGORY_CARD: { width: '323px', height: '105px', borderRadius: '8px' },
  IMAGE_CARD: { width: '412px', height: '232px', borderRadius: '0px' },
  LIST_ITEM: { width: '100%', height: '124px', borderRadius: '0px' },
} as const;

export type SkeletonSizeType = keyof typeof SKELETON_SIZES;
