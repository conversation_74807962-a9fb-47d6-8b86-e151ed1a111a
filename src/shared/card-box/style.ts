import styled from 'styled-components';

interface ICardBoxProps {
  focused: boolean;
  imageUrl?: string;
  width?: string;
  height?: string;
  borderRadius?: string;
  padding?: string;
}

export const StyledCardBox = styled.div.withConfig({
  shouldForwardProp: prop => !['focused', 'imageUrl', 'borderRadius', 'padding'].includes(prop),
})<ICardBoxProps>`
  width: ${({ width }) => width ?? '412px'};
  height: ${({ height }) => height ?? '232px'};
  position: relative;
  .video-card {
    position: absolute;
    inset: 0px;
  }
  /* background-image: ${({ imageUrl, theme }) =>
    imageUrl ? `url(${imageUrl})` : theme.gameTileOverlayGradient};
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat; */
  border-color: ${props => props?.theme?.surfaceWhite};
  border-style: solid;
  border-width: ${({ focused }) => (focused ? '4px' : 0)};
  box-sizing: border-box;
  border-radius: ${({ borderRadius }) => borderRadius ?? '7px'};
  position: relative;
  display: flex;
  align-items: flex-end;
  padding: ${({ padding }) => padding ?? '16px'};
`;
