import { ImageVideo } from 'shared/imageVideo';
import { StyledCardBox } from './style';

export interface ICardBoxComponentProps {
  readonly focused: boolean;
  readonly imageUrl?: string;
  readonly videoUrl?: string;
  readonly width?: string;
  readonly height?: string;
  readonly borderRadius?: string;
  readonly padding?: string;
  readonly children?: React.ReactNode;
  readonly className?: string;
}

export function CardBox({
  focused,
  imageUrl,
  width = '412px',
  height = '232px',
  borderRadius = '7px',
  padding = '20px',
  children,
  className,
  videoUrl,
}: ICardBoxComponentProps) {
  return (
    <StyledCardBox
      focused={focused}
      imageUrl={imageUrl}
      width={width}
      height={height}
      borderRadius={borderRadius}
      padding={padding}
      className={className}
    >
      <ImageVideo
        imageUrl={imageUrl}
        videoUrl={videoUrl}
        isFocused={focused}
        width="100%"
        borderRadius={borderRadius}
        height="100%"
        className="video-card"
      />

      {children}
    </StyledCardBox>
  );
}
