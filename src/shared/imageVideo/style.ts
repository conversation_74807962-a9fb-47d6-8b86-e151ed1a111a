import styled from 'styled-components';

export const Container = styled.div<{
  width?: number | string;
  height?: number | string;
  borderRadius?: number | string;
}>`
  position: relative;
  width: ${props => props.width};
  height: ${props => props.height};
  border-radius: ${props => props.borderRadius};
  overflow: hidden;
  cursor: pointer;
`;

export const ImageThumbnail = styled.img<{ fit?: string }>`
  width: 100%;
  height: 100%;
  object-fit: ${props => props.fit};
  display: block;
`;

export const VideoContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

export const Video = styled.video<{ fit?: string }>`
  width: 100%;
  height: 100%;
  object-fit: ${props => props.fit};
`;
