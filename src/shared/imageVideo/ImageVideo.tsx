import React, { useEffect, useState, useRef, useCallback } from 'react';
import { Container, ImageThumbnail, Video } from './style';

interface IImageVideoProps {
  imageUrl?: string;
  videoUrl?: string;
  width?: number | string;
  height?: number | string;
  borderRadius?: number | string;
  fit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  autoPlay?: boolean;
  isFocused?: boolean;
  className?: string;
  delay?: number; // Make delay configurable
}

export const ImageVideo = (props: IImageVideoProps) => {
  const {
    autoPlay,
    borderRadius,
    className,
    fit = 'cover',
    height,
    imageUrl,
    isFocused,
    videoUrl,
    width,
    delay = 3000,
  } = props;

  const [showVideo, setShowVideo] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Memoized function to handle video transition
  const handleVideoTransition = useCallback(() => {
    if (!videoUrl) return;

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      setShowVideo(true);
    }, delay);
  }, [videoUrl, delay]);

  // Combined effect for both autoPlay and isFocused
  useEffect(() => {
    if (autoPlay || isFocused) {
      handleVideoTransition();
    } else {
      // Clear timeout and hide video when conditions are not met
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      setShowVideo(false);

      // Pause video if it's currently playing
      if (videoRef.current) {
        videoRef.current.pause();
      }
    }
  }, [autoPlay, isFocused, handleVideoTransition]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Clean up video resources
      if (videoRef.current) {
        const video = videoRef.current;
        video.pause();
        video.currentTime = 0;
        video.src = '';
        video.load();
      }
    };
  }, []);

  // Handle video play when it becomes visible
  useEffect(() => {
    if (showVideo && videoRef.current) {
      const video = videoRef.current;
      const playPromise = video.play();

      if (playPromise !== undefined) {
        playPromise.catch(error => {
          console.warn('Video autoplay failed:', error);
          // Fallback to showing image if video fails
          setShowVideo(false);
        });
      }
    }
  }, [showVideo]);

  // Memoized error handler
  const handleVideoError = useCallback(() => {
    console.warn('Video failed to load:', videoUrl);
    setShowVideo(false);
  }, [videoUrl]);

  const handleImageError = useCallback(() => {
    console.warn('Image failed to load:', imageUrl);
  }, [imageUrl]);

  return (
    <Container width={width} height={height} borderRadius={borderRadius} className={className}>
      {showVideo && videoUrl ? (
        <Video
          ref={videoRef}
          fit={fit}
          muted
          loop
          playsInline
          preload="metadata"
          onError={handleVideoError}
        >
          <source src={videoUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </Video>
      ) : (
        imageUrl && (
          <ImageThumbnail src={imageUrl} alt="thumb-image" fit={fit} onError={handleImageError} />
        )
      )}
    </Container>
  );
};
