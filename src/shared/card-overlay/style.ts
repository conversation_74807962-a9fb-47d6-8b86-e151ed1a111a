import styled from 'styled-components';
import { gradients } from 'styles/theme/gradients';

interface CardOverlayProps {
  focused: boolean;
  borderRadius?: string;
}

export const OverlayWrapper = styled.div.withConfig({
  shouldForwardProp: prop => !['focused', 'borderRadius'].includes(prop),
})<CardOverlayProps>`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: ${gradients.gameTileOverlayGradient};
  border-radius: ${({ borderRadius }) => borderRadius || '7px'};
  opacity: ${({ focused }) => (focused ? 0 : 1)};
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
`;
