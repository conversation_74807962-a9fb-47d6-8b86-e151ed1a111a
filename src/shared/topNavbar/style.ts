import styled from 'styled-components';
import { semanticBorderRadius } from 'styles/theme/borderRadius';
import { BodyLG } from 'styles/theme/typography';

export const NavbarContainer = styled.header`
  width: 100%;
  height: 128px;
  background: ${({ theme }) => theme.bgColor};
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  box-sizing: border-box;
  position: relative;
  z-index: 100;
`;

export const LogoSection = styled.div`
  display: flex;
  align-items: center;
`;

export const Logo = styled.img`
  height: 64px;
  width: auto;
  margin-right: 15px;
  object-fit: contain;
`;

export const AppName = styled.div`
  color: ${({ theme }) => theme.textPrimary};
  font-size: 28px;
  font-weight: 600;
  font-family:
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
`;

export const ProfileSection = styled.div<{ $focused: boolean }>`
  display: flex;
  align-items: center;
  padding-inline: 24px;
  border-radius: ${semanticBorderRadius.interactive.large};
  background: ${({ $focused, theme }) =>
    $focused ? theme.surfaceSecondaryOpacity60 : theme.bgColor};
  border: ${({ $focused, theme }) =>
    $focused ? `2px solid ${theme.outlineStrokeFocusWhite}` : '2px solid transparent'};
  transition: all 0.3s ease;
  cursor: pointer;
  gap: 16px;
`;

export const ProfileInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
  margin-right: 32px;
`;

export const UserSection = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

export const OnlineStatus = styled(BodyLG).attrs({ as: 'div' })`
  display: flex;
  align-items: center;
  gap: 8px;
  color: ${({ theme }) => theme.textSecondary};
`;
