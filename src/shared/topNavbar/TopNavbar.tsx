import Kids from 'shared/profileIage/Kids';
import { HeadingSM } from 'styles/theme/typography';
import phyndLogo from 'assets/logo/PHYND Alpha Logo.png';
import ProfileImage from '../profileIage/ProfileImage';
import {
  Logo,
  LogoSection,
  NavbarContainer,
  OnlineStatus,
  ProfileInfo,
  ProfileSection,
  UserSection,
} from './style';
import { OnlineDot } from 'styles/sharedStyles';
import { useAppSelector } from 'hooks/redux.hooks';

export function TopNavbar() {
  const { profile, isOnline } = useAppSelector(state => state?.user);
  return (
    <NavbarContainer>
      <LogoSection>
        <Logo src={phyndLogo} alt="PHYND Alpha Logo" />
      </LogoSection>

      <ProfileSection $focused={false}>
        <ProfileImage
          imageUrl={profile?.user?.dp_url}
          alt={profile?.user?.display_name}
          size={64}
        />
        <ProfileInfo>
          <UserSection>
            <HeadingSM>{profile?.user?.display_name}</HeadingSM>
            <OnlineStatus as={'div'}>
              <OnlineDot isOnline={isOnline} />
              {isOnline ? 'Online' : 'Offline'}
            </OnlineStatus>
          </UserSection>
        </ProfileInfo>
        <Kids />{' '}
      </ProfileSection>
    </NavbarContainer>
  );
}
