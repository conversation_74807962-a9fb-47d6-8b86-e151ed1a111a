import React, { useEffect, useState } from 'react';
import { PrimaryButton } from 'shared/buttons';
import { OnlineDot, Row } from 'styles/sharedStyles';
import { ButtonWrap, CardWrap, GameImage, ProfileIcon } from './style';
import { handleLogoError } from 'utils/helpers.utils';
import { BodyXL, HeadingMD } from 'styles/theme/typography';
import {
  FocusableComponentLayout,
  FocusDetails,
  useFocusable,
} from '@noriginmedia/norigin-spatial-navigation';
import { useTheme } from 'styled-components';

interface IListItemCardProps {
  imageSrc?: string;
  isGame?: boolean;
  isOnline?: boolean;
  title: string;
  isExpandable?: boolean;
  onClick?: () => void;
  onAcceptClick?: () => void | Promise<void>;
  onDenyClick?: () => void | Promise<void>;
  focusKey?: string;
  showOnline?: boolean;
  onFocus?: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  onCardFocused?: (props: object) => void; // New callback for when focused
}

export const ListItemCard: React.FC<IListItemCardProps> = ({
  title,
  imageSrc,
  isGame,
  isOnline,
  isExpandable,
  onClick,
  onAcceptClick,
  onDenyClick,
  focusKey,
  onCardFocused,
  onFocus,
  showOnline,
}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);
  const [isAcceptButton, setIsAcceptButton] = useState<boolean | null>(null);
  const [isAcceptLoading, setIsAcceptLoading] = useState(false);
  const [isDenyLoading, setIsDenyLoading] = useState(false);

  const { ref, focused } = useFocusable({
    focusKey: focusKey ?? `${title}-`, // Use provided focus key or fallback
    onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => {
      // Call the original onFocus callback
      onFocus?.(layout, props, details);

      // Call the onInit callback when focused
      if (onCardFocused) {
        onCardFocused(props);
      }
    },
    onEnterPress: () => {
      if (isExpandable) {
        if (isAcceptButton === null) {
          setExpanded(prev => !prev);
          setIsAcceptButton(null);
        } else {
          setExpanded(true);

          if (isAcceptButton === null) {
            setExpanded(prev => !prev);
            setIsAcceptButton(true);
          } else {
            (async () => {
              setExpanded(true);

              const isAccept = isAcceptButton;
              const setLoading = isAccept ? setIsAcceptLoading : setIsDenyLoading;
              const action = isAccept ? onAcceptClick : onDenyClick;

              setLoading(true);
              try {
                await action?.();
              } catch (error) {
                console.error(`${isAccept ? 'Accept' : 'Deny'} failed:`, error);
              } finally {
                setLoading(false);
              }
            })();
          }
        }
      } else {
        onClick?.();
      }
    },
    onArrowPress: (arrow: string) => {
      if (arrow === 'left' || arrow === 'right') {
        setIsAcceptButton(prev => !prev);
      } else if (arrow === 'up' || arrow === 'down') {
        setIsAcceptButton(null);
      }

      return !expanded;
    },
    extraProps: {
      title,
      imageSrc,
      isGame,
      isOnline,
      isExpandable,
    },
  });

  useEffect(() => {
    if (expanded) setIsAcceptButton(true);
  }, [expanded]);

  const renderButton = () => (
    <ButtonWrap>
      <PrimaryButton
        focusKey="accept-key"
        height="49px"
        width="100%"
        fontSize="22px"
        isActive={isAcceptButton === true}
        bgColor={theme?.btnSecondary}
        isLoading={isAcceptLoading}
      >
        Accept
      </PrimaryButton>
      <PrimaryButton
        focusKey="denny-key"
        height="49px"
        width="100%"
        fontSize="22px"
        isActive={isAcceptButton === false}
        bgColor={theme?.btnSecondary}
        isLoading={isDenyLoading}
      >
        Deny
      </PrimaryButton>
    </ButtonWrap>
  );

  return (
    <CardWrap isExpanded={expanded} ref={ref} focused={focused}>
      <Row align="center" justify="space-between">
        <Row align="center" gap="20px">
          {isGame ? (
            <GameImage src={imageSrc} />
          ) : (
            <ProfileIcon src={imageSrc ?? ' '} onError={handleLogoError} />
          )}
          <HeadingMD>{title}</HeadingMD>
        </Row>
        {showOnline && (
          <Row align="center" gap="16px">
            <OnlineDot isOnline={isOnline} />
            <BodyXL>{isOnline ? 'Online' : 'Offline'}</BodyXL>
          </Row>
        )}
      </Row>
      {isExpandable && expanded && renderButton()}
    </CardWrap>
  );
};
