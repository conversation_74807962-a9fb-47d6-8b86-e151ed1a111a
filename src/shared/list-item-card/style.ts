import styled from 'styled-components';
import { Col, Row } from 'styles/sharedStyles';

export const ButtonWrap = styled(Row)`
  gap: 16px;
  align-items: center;
`;

interface ICardWrap {
  isExpanded?: boolean;
  focused?: boolean;
}

export const CardWrap = styled(Col)<ICardWrap>`
  width: 100%;
  gap: 20px;
  height: ${props => (props?.isExpanded ? '181px' : '121px')};
  transition: height 0.5s ease-in-out;
  background: ${props => props?.theme?.surfaceSecondaryOpacity100};
  padding: 16px 40px;
  justify-content: center;
  border: 4px solid ${props => (props?.focused ? props?.theme?.surfaceWhite : 'transparent')};

  ${ButtonWrap} {
    height: ${props => (props?.isExpanded ? '50px' : '0')};
    transition: height 1s ease-in-out;
    overflow: hidden;
  }
`;

export const ProfileIcon = styled.img`
  width: 80px;
  height: 80px;
  object-fit: cover;
  border: 2px solid ${props => props?.theme?.surfaceWhite};
  border-radius: 50%;
`;

export const GameImage = styled.img`
  width: 157px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
`;
