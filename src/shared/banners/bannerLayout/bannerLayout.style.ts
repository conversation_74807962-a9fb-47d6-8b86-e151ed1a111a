import styled from 'styled-components';
import { BannerType } from 'enums/banner-type.enums';

interface BannerContainerProps {
  $backgroundImage: string;
  $focused?: boolean;
  $bannerType?: BannerType;
}

interface BannerOverlayContainerProps {
  $focused?: boolean;
  $bannerType?: BannerType;
}

// Helper function to get height based on banner type and focus state
const getHeight = (focused: boolean, bannerType: BannerType) => {
  if (bannerType === BannerType.OMBRE) {
    return focused ? '800px' : '680px'; // temp for library
  }
  // Standard banner type
  return focused ? '680px' : '570px';
};

// Helper function to get padding based on banner type and focus state
const getPadding = (focused: boolean, bannerType: BannerType) => {
  if (bannerType === BannerType.OMBRE) {
    return focused ? '56px 32px 140px 48px' : '56px 32px 180px 48px';
  }
  // Standard banner type
  // return focused ? '56px 32px 120px 48px' : '56px 32px 180px 48px'; // temp padding for library
  return focused ? '56px 32px 56px 48px' : '56px 32px 56px 48px';
};

// Helper function to get translate Y offset based on banner type and focus state
const getTranslateY = (focused: boolean, bannerType: BannerType) => {
  if (bannerType === BannerType.OMBRE) {
    return focused ? 'translate(-50%, calc(-50% - 64px))' : 'translate(-50%, calc(-50% - 120px))';
  }
  // Standard banner type
  return focused ? 'translate(-50%, calc(-50% + 24px))' : 'translate(-50%, calc(-50% + 24px))';
};

export const BannerContainer = styled.div<BannerContainerProps>`
  width: 100%;
  height: ${({ $focused = false, $bannerType = BannerType.STANDARD }) =>
    getHeight($focused, $bannerType)};
  background-image: url(${({ $backgroundImage }) => $backgroundImage});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: height 0.3s ease-in-out;
`;

export const BannerOverlayContainer = styled.div<BannerOverlayContainerProps>`
  display: flex;
  width: inherit;
  height: ${({ $focused = false, $bannerType = BannerType.STANDARD }) =>
    getHeight($focused, $bannerType)};
  padding: ${({ $focused = false, $bannerType = BannerType.STANDARD }) =>
    getPadding($focused, $bannerType)};
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;

  transition:
    height 0.3s ease-in-out,
    transform 0.3s ease-in-out;
  z-index: 2;
`;
