import React from 'react';
import { BannerType } from 'enums/banner-type.enums';
import { GAME_MEDIA } from 'enums/game-media.enums';
import { BannerContainer, BannerOverlayContainer } from './bannerLayout.style';
import BannerOverlayMask from '../components/BannerOverlayMask';
import BannerMedia from './BannerMedia';

interface BannerLayoutProps {
  backgroundImage?: string;
  mediaUrl?: string;
  mediaType?: GAME_MEDIA;
  children?: React.ReactNode;
  focused?: boolean;
  bannerType?: BannerType;
}

const BannerLayout: React.FC<BannerLayoutProps> = ({
  backgroundImage = 'https://cdn.pixabay.com/photo/2023/01/28/20/12/ai-generated-7751663_1280.jpg',
  mediaUrl,
  mediaType = GAME_MEDIA.IMAGE,
  children,
  focused = false,
  bannerType = BannerType.STANDARD,
}) => {
  // Use mediaUrl if provided, otherwise fall back to backgroundImage
  const finalMediaUrl = mediaUrl || backgroundImage;

  return (
    <BannerContainer $backgroundImage="" $focused={focused} $bannerType={bannerType}>
      <BannerMedia mediaUrl={finalMediaUrl} mediaType={mediaType} />
      <BannerOverlayMask />
      <BannerOverlayContainer $focused={focused} $bannerType={bannerType}>
        {children}
      </BannerOverlayContainer>
    </BannerContainer>
  );
};

export default BannerLayout;
