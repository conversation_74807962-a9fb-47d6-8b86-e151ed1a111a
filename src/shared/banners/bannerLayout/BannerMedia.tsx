import React from 'react';
import { GAME_MEDIA } from 'enums/game-media.enums';

interface BannerMediaProps {
  mediaUrl: string;
  mediaType: GAME_MEDIA;
}

const BannerMedia: React.FC<BannerMediaProps> = ({ mediaUrl, mediaType }) => {
  const mediaStyle = {
    width: '100%',
    height: '100%',
    objectFit: 'cover' as const,
  };

  if (mediaType === GAME_MEDIA.VIDEO || mediaType === GAME_MEDIA.TRAILER) {
    return <video autoPlay={true} muted={true} loop={true} src={mediaUrl} style={mediaStyle} />;
  }

  return <img src={mediaUrl} alt="banner-media" style={mediaStyle} />;
};

export default BannerMedia;
