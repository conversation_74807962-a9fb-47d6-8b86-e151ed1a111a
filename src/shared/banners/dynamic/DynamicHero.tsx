import React from 'react';
import DynamicPromoOverlay from './heroOverlay/DynamicPromoOverlay';
import BannerOverlayContent from '../components/BannerOverlayContent';
import GameDynamic from './overlayContent/GameDynamic';
import DynamicVideoOverlay from './heroOverlay/DynamicVideoOverlay';
import { ContentType } from 'types/focused-card.types';

interface GameData {
  gameLogoUrl?: string;
  gameTitle?: string;
  stats?: {
    players?: string;
    followers?: string;
    likes?: string;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

interface VideoData {
  gameLogoUrl?: string;
  gameTitle?: string;
  publisherInfo?: {
    imageUrl?: string;
    publisherName?: string;
    isVerified?: boolean;
  };
  stats?: {
    uploadTime?: string;
    views?: string;
    likes?: string;
  };
  onPlayClick?: () => void;
  onSaveClick?: () => void;
}

interface EventData {
  gameLogoUrl?: string;
  eventTitle?: string;
  stats?: {
    dropDate?: string;
    expires?: string;
    views?: string;
    saves?: string;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

type DynamicContentData = GameData | VideoData | EventData;

interface DynamicHeroProps {
  contentType: ContentType;
  data?: DynamicContentData;
}

const DynamicHero: React.FC<DynamicHeroProps> = ({ contentType, data = {} }) => {
  const renderDynamicContent = (contentType: ContentType) => {
    switch (contentType) {
      case ContentType.GAME:
      case ContentType.IMAGE:
        return (
          <BannerOverlayContent
            bottomLeft={<GameDynamic gameLogoUrl="" {...(data as GameData)} />}
          />
        );
      case ContentType.VIDEO:
      case ContentType.TRAILER:
        return (
          <DynamicVideoOverlay
            topLeftText={ContentType.VIDEO ? 'New Video' : 'New Trailer'}
            videoData={{ gameLogoUrl: '', ...(data as VideoData) }}
          />
        );
      case ContentType.EVENT:
      case ContentType.OFFER:
        return (
          <DynamicPromoOverlay
            topLeftText={ContentType.OFFER ? 'New Offer' : 'New Event'}
            eventData={{ gameLogoUrl: '', ...(data as EventData) }}
          />
        );
      default:
        return null;
    }
  };

  return renderDynamicContent(contentType);
};

export default DynamicHero;
