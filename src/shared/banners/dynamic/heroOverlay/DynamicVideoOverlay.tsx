import React from 'react';
import BannerOverlayContent from 'shared/banners/components/BannerOverlayContent';
import VideoDyanmic from '../overlayContent/VideoDyanmic';
import MediaTileBadge from 'shared/badges/mediaTileBadge';

interface DynamicVideoOverlayProps {
  topLeftText?: string;
  bottomRightText?: string;
  videoData?: {
    gameLogoUrl: string;
    gameTitle?: string;
    publisherInfo?: {
      imageUrl?: string;
      publisherName?: string;
      isVerified?: boolean;
    };
    stats?: {
      uploadTime?: string;
      views?: string;
      likes?: string;
    };
    onPlayClick?: () => void;
    onSaveClick?: () => void;
  };
}

// Dummy data for default props
const defaultVideoData = {
  gameLogoUrl: '/src/assets/logo/GameTitleLogo.png',
  gameTitle: 'Sample Game Title',
  publisherInfo: {
    imageUrl: '',
    publisherName: 'Publisher',
    isVerified: false,
  },
  stats: {
    uploadTime: 'Uploaded: 2 Hours Ago',
    views: '21K',
    likes: '34K',
  },
  onPlayClick: () => console.log('Play clicked'),
  onSaveClick: () => console.log('Save clicked'),
};

const DynamicVideoOverlay: React.FC<DynamicVideoOverlayProps> = ({
  topLeftText = 'New Trailer',
  bottomRightText = '8 min 14 sec',
  videoData = defaultVideoData,
}) => {
  return (
    <BannerOverlayContent
      topLeft={<MediaTileBadge>{topLeftText}</MediaTileBadge>}
      bottomLeft={<VideoDyanmic {...videoData} />}
      bottomRight={<MediaTileBadge showBorder={false}>{bottomRightText}</MediaTileBadge>}
      bottomAlignItems="flex-start"
    />
  );
};

export default DynamicVideoOverlay;
