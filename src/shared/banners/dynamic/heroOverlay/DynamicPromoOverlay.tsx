import React from 'react';
import BannerOverlayContent from 'shared/banners/components/BannerOverlayContent';
import EventDynamic from '../overlayContent/EventDyanamic';
import MediaTileBadge from 'shared/badges/mediaTileBadge';

interface DynamicPromoOverlayProps {
  topLeftText?: string;
  bottomRightText?: string;
  eventData?: {
    gameLogoUrl: string;
    eventTitle?: string;
    stats?: {
      dropDate?: string;
      expires?: string;
      saveCount: number;
      playCount: number;
    };
    onMoreInfoClick?: () => void;
    onSaveClick?: () => void;
  };
}

// Dummy data for default props
const defaultEventData = {
  gameLogoUrl: '/src/assets/logo/GameTitleLogo.png',
  eventTitle: 'Season 3 Battlepass',
  stats: {
    dropDate: '7/28/25',
    expires: '8/28/25',
    saveCount: 0,
    playCount: 0,
  },
  onMoreInfoClick: () => console.log('More info clicked'),
  onSaveClick: () => console.log('Save clicked'),
};

const DynamicPromoOverlay: React.FC<DynamicPromoOverlayProps> = ({
  topLeftText = 'New Event',
  bottomRightText,
  eventData = defaultEventData,
}) => {
  return (
    <BannerOverlayContent
      topLeft={<MediaTileBadge>{topLeftText}</MediaTileBadge>}
      bottomLeft={<EventDynamic {...eventData} />}
      bottomRight={bottomRightText ? <MediaTileBadge>{bottomRightText}</MediaTileBadge> : undefined}
    />
  );
};

export default DynamicPromoOverlay;
