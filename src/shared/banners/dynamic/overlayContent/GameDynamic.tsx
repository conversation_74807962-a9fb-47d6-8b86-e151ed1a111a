import React from 'react';
import { IoGameController, IoHeart, IoPeople } from 'react-icons/io5';
import { FaLightbulb, FaCircle } from 'react-icons/fa';
import {
  ButtonsContainer,
  GameLogo,
  GameLogoContainer,
  GameOmbreContainer,
  GenreItem,
  GenresContainer,
  RatingText,
  StatIcon,
  StatItem,
  StatsContainer,
  StatText,
} from '../../ombre/GameOmbre.style';
import { PrimaryButton } from 'shared/buttons';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import { formatNumberWithSuffix } from 'utils/numberUtils';

// Wrapper components to match IconProps interface
interface IconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const GameControllerWrapper: React.FC<IconProps> = ({ fill, width = 40, height = 40 }) => (
  <IoGameController size={width} color={fill} />
);

const PeopleIconWrapper: React.FC<IconProps> = ({ fill, width = 24, height = 24 }) => (
  <IoPeople size={width} color={fill} />
);

const HeartIconWrapper: React.FC<IconProps> = ({ fill, width = 24, height = 24 }) => (
  <IoHeart size={width} color={fill} />
);

const LightbulbIconWrapper: React.FC<IconProps> = ({ fill, width = 40, height = 40 }) => (
  <FaLightbulb size={width} color={fill} />
);

const DotIconWrapper: React.FC<IconProps> = ({ fill, width = 16, height = 16 }) => (
  <FaCircle size={width} color={fill} />
);

interface GameOmbreProps {
  gameLogoUrl: string;
  gameTitle?: string;
  stats?: {
    players?: number;
    followers?: number;
    likes?: number;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

const GameDynamic: React.FC<GameOmbreProps> = ({
  gameLogoUrl,
  gameTitle = 'Game Title',
  stats = {
    players: 41,
    followers: 1400,
    likes: 3400,
    rating: 'E for Everyone',
  },
  genres = ['Adventure', 'Endless Runner', 'Racing', 'Action'],
  onPlayClick,
  onMoreInfoClick,
}) => {
  return (
    <GameOmbreContainer>
      {/* Game Logo Section */}
      <GameLogoContainer>
        <GameLogo src={gameLogoUrl} alt={gameTitle} />
      </GameLogoContainer>

      {/* Stats Section */}
      <StatsContainer>
        <StatItem>
          <StatIcon>
            <IoGameController size={24} />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats?.players ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <StatIcon>
            <PeopleIconWrapper />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats.followers ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <StatIcon>
            <HeartIconWrapper />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats.likes ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <RatingText>{stats.rating}</RatingText>
      </StatsContainer>

      {/* Genres Section */}
      <GenresContainer>
        {genres.map((genre, index) => (
          <React.Fragment key={index}>
            <GenreItem>{genre}</GenreItem>
            {index < genres.length - 1 && <DotIconWrapper />}
          </React.Fragment>
        ))}
      </GenresContainer>

      {/* Buttons Section */}
      <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={GameControllerWrapper}
          onClick={onPlayClick}
          focusKey="play-button"
        >
          Play
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={LightbulbIconWrapper}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More info
        </PrimaryButton>
      </ButtonsContainer>
    </GameOmbreContainer>
  );
};

export default GameDynamic;
