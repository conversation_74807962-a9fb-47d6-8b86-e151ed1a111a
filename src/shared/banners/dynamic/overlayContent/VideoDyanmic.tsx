import React from 'react';
import { IoEye, IoHeart } from 'react-icons/io5';
import PublisherInfo from 'shared/publisherInfo';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import { DisplayLG } from 'styles/theme/typography';
import {
  GameOmbreContainer,
  StatItem,
  StatsContainer,
  StatText,
} from '../../ombre/GameOmbre.style';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import { formatTimeAgo } from 'utils/helpers.utils';

// Wrapper components to match IconProps interface

interface VideoOmbreProps {
  gameTitle?: string;
  publisherInfo?: {
    imageUrl?: string;
    publisherName?: string;
    isVerified?: boolean;
  };
  stats?: {
    uploadTime?: string;
    views?: string;
    likes?: string;
    playCount?: string;
    followers?: string;
  };
  onPlayClick?: () => void;
  onSaveClick?: () => void;
}

const VideoDyanmic: React.FC<VideoOmbreProps> = ({
  gameTitle = 'Game Title',
  publisherInfo = {
    imageUrl: '',
    publisherName: 'Publisher',
    isVerified: false,
  },
  stats = {
    uploadTime: 'Uploaded: 2 Hours Ago',
    views: '21K',
    likes: '34K',
  },
  onPlayClick,
  onSaveClick,
}) => {
  return (
    <GameOmbreContainer>
      {/* Video Logo Section */}
      <DisplayLG>{gameTitle}</DisplayLG>

      <PublisherInfo
        imageUrl={publisherInfo?.imageUrl ?? ''}
        publisherName={publisherInfo?.publisherName ?? 'Publisher'}
        profileSize={80}
        iconSize={40}
        isVerified={publisherInfo?.isVerified}
      />

      {/* Stats Section */}
      <StatsContainer>
        <StatText>{formatTimeAgo(stats?.uploadTime ?? '')}</StatText>
        <VerticalSeparator height="24px" />
        <IoEye size={40} />
        <StatText>{formatNumberWithSuffix(Number(stats.playCount))}</StatText>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoHeart size={40} />

          <StatText>{formatNumberWithSuffix(Number(stats.likes))}</StatText>
        </StatItem>
      </StatsContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoPlay size={40} />}
          onClick={onPlayClick}
          focusKey="play-button"
        >
          Watch
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoBookmarkOutline size={40} />}
          onClick={onSaveClick}
          focusKey="save-button"
        >
          Save
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default VideoDyanmic;
