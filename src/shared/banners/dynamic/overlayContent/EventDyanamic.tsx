import React from 'react';
import { IoBookmark, IoEye } from 'react-icons/io5';

import VerticalSeparator from 'shared/separators/VerticalSeparator';
import {
  GameLogo,
  GameLogoContainer,
  GameOmbreContainer,
  StatItem,
  StatsContainer,
  StatText,
  TitleText,
} from '../../ombre/GameOmbre.style';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import { formatDateTime } from 'utils/dateUtils';

interface EventOmbreProps {
  gameLogoUrl: string;
  eventTitle?: string;
  stats?: {
    dropDate?: string;
    expires?: string;
    saveCount: number;
    playCount: number;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

const EventDynamic: React.FC<EventOmbreProps> = ({
  gameLogoUrl,
  eventTitle = 'Season 3 Battlepass',
  stats = {
    dropDate: '7/28/25',
    expires: '8/28/25',
    saveCount: 0,
    playCount: 0,
  },
  onMoreInfoClick,
  onSaveClick,
}) => {
  return (
    <GameOmbreContainer>
      {/* Game Logo Section */}
      <GameLogoContainer>
        <GameLogo src={gameLogoUrl} alt={eventTitle} />
      </GameLogoContainer>

      {/* Event Title Section */}
      <TitleText>{eventTitle}</TitleText>

      {/* Stats Section */}
      <StatsContainer>
        <StatText>Drop Date: {formatDateTime(stats.dropDate ?? '')}</StatText>
        {stats?.expires && (
          <>
            <VerticalSeparator height="32px" />
            <StatText>Expires: {formatDateTime(stats.expires)}</StatText>
          </>
        )}
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoEye size={36} />
          <StatText>{formatNumberWithSuffix(stats.playCount)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoBookmark size={36} />

          <StatText>{formatNumberWithSuffix(stats.saveCount)}</StatText>
        </StatItem>
      </StatsContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          icon={<FaLightbulb size={40} />}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More Game Info
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoBookmarkOutline size={40} />}
          onClick={onSaveClick}
          focusKey="save-button"
        >
          Save
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default EventDynamic;
