import React from 'react';
import { IoCheckmark } from 'react-icons/io5';
import ProfileImage from 'shared/profileIage/ProfileImage';
import { CheckmarkCircle } from 'shared/publisherInfo/PublisherInfo.style';
import { VerticalSeparator } from 'shared/separators';
import { Col, Row } from 'styles/sharedStyles';
import { BodyLG, BodyMD, DisplayLG } from 'styles/theme/typography';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import { PublisherAvatarContainer, VerifiedIcon } from './PublisherDynamic.style';

// Verified checkmark SVG component

interface PublisherStats {
  followers?: number;
  games?: number;
  upcomingEvents?: number;
}

interface PublisherDynamicProps {
  publisherName?: string;
  publisherLogoUrl?: string;
  isVerified?: boolean;
  stats?: PublisherStats;
}

const PublisherDynamic: React.FC<PublisherDynamicProps> = ({
  publisherName = 'Studio 369 Official',
  publisherLogoUrl,
  isVerified = true,
  stats = {
    followers: 1400,
    games: 16,
    upcomingEvents: 14,
  },
}) => {
  return (
    <Col gap="24px">
      {/* Publisher Header with Avatar and Name */}

      <PublisherAvatarContainer>
        <ProfileImage imageUrl={publisherLogoUrl} size={200} />
        {isVerified && (
          <VerifiedIcon>
            <CheckmarkCircle size={42}>
              <IoCheckmark size={42 * 0.6} />
            </CheckmarkCircle>
          </VerifiedIcon>
        )}
      </PublisherAvatarContainer>

      <Col gap="40px">
        <DisplayLG> {publisherName}</DisplayLG>

        {/* Stats Section */}
        <Row gap="16px" align="center">
          <Col align="center">
            <BodyMD>{formatNumberWithSuffix(stats.followers ?? 0)}</BodyMD>
            <BodyLG>Followers</BodyLG>
          </Col>

          <VerticalSeparator height="56px" width="2px" />

          <Col align="center" gap="0">
            <BodyMD>{stats.games ?? 0}</BodyMD>
            <BodyLG>Games</BodyLG>
          </Col>
          <VerticalSeparator height="56px" width="2px" />

          <Col align="center">
            <BodyMD>{stats.upcomingEvents ?? 0}</BodyMD>
            <BodyLG>Upcoming Events</BodyLG>
          </Col>
        </Row>
      </Col>
    </Col>
  );
};

export default PublisherDynamic;
