import React from 'react';
import { FaLightbulb } from 'react-icons/fa';
import { IoBookmark, IoBookmarkOutline, IoEye } from 'react-icons/io5';

import { PrimaryButton } from 'shared/buttons';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import {
  ButtonsContainer,
  GameLogo,
  GameLogoContainer,
  GameOmbreContainer,
  StatItem,
  StatsContainer,
  StatText,
  TitleText,
} from '../../ombre/GameOmbre.style';
import { formatDateTime } from 'utils/dateUtils';

interface EventOmbreProps {
  gameLogoUrl: string;
  eventTitle?: string;
  stats?: {
    dropDate?: string;
    expires?: string;
    views?: string;
    saves?: string;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

const EventDynamic: React.FC<EventOmbreProps> = ({
  gameLogoUrl,
  eventTitle = 'Season 3 Battlepass',
  stats = {
    dropDate: '7/28/25',
    expires: '8/28/25',
    views: '21K',
    saves: '8K',
  },
  onMoreInfoClick,
  onSaveClick,
}) => {
  return (
    <GameOmbreContainer>
      {/* Game Logo Section */}
      <GameLogoContainer>
        <GameLogo src={gameLogoUrl} alt={eventTitle} />
      </GameLogoContainer>

      {/* Event Title Section */}
      <TitleText>{eventTitle}</TitleText>

      {/* Stats Section */}
      <StatsContainer>
        <StatText>Drop Date: {formatDateTime(stats?.dropDate ?? '')}</StatText>
        {stats?.expires && (
          <>
            <VerticalSeparator height="24px" />
            <StatText>Expires: {formatDateTime(stats.expires)}</StatText>
          </>
        )}
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoEye size={32} />
          <StatText>{stats.views}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoBookmark size={32} />

          <StatText>{stats.saves}</StatText>
        </StatItem>
      </StatsContainer>

      {/* Buttons Section */}
      <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          icon={<FaLightbulb size={40} />}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More Game Info
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoBookmarkOutline size={40} />}
          onClick={onSaveClick}
          focusKey="save-button"
        >
          Save
        </PrimaryButton>
      </ButtonsContainer>
    </GameOmbreContainer>
  );
};

export default EventDynamic;
