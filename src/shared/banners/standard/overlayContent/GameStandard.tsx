import React from 'react';
import { FaCircle } from 'react-icons/fa';
import { IoGameController, IoHeart, IoPeople } from 'react-icons/io5';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import {
  GameOmbreContainer,
  GenreItem,
  GenresContainer,
  RatingText,
  StatItem,
  StatsContainer,
  StatText,
} from '../../ombre/GameOmbre.style';
import { formatNumberWithSuffix } from 'utils/numberUtils';

// Wrapper components to match IconProps interface
interface IconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const DotIconWrapper: React.FC<IconProps> = ({ fill, width = 16 }) => (
  <FaCircle size={width} color={fill} />
);

interface GameOmbreProps {
  gameLogoUrl: string;
  gameTitle?: string;
  stats?: {
    playCount?: number;
    followers?: number;
    likes?: number;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

const GameStandard: React.FC<GameOmbreProps> = ({
  stats = {
    playCount: '41K',
    followers: '1.4K',
    likes: '34K',
    rating: 'E for Everyone',
  },
  genres = [],
}) => {
  return (
    <GameOmbreContainer>
      <StatsContainer>
        <StatItem>
          <IoGameController size={32} />
          <StatText>{formatNumberWithSuffix(Number(stats?.playCount))}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoPeople size={32} />
          <StatText>{formatNumberWithSuffix(Number(stats.followers))}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoHeart size={32} />
          <StatText>{formatNumberWithSuffix(Number(stats.likes))}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <RatingText>{stats.rating}</RatingText>
      </StatsContainer>

      {/* Genres Section */}
      <GenresContainer>
        {genres.map((genre, index) => (
          <React.Fragment key={index}>
            <GenreItem>{genre}</GenreItem>
            {index < genres.length - 1 && <DotIconWrapper />}
          </React.Fragment>
        ))}
      </GenresContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoGameController size={40} />}
          onClick={onPlayClick}
          focusKey="play-button"
        >
          Play
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<FaLightbulb size={40} />}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More info
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default GameStandard;
