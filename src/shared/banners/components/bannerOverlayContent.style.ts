import styled from 'styled-components';

export const OverlayTopSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
`;

export const OverlayBottomSection = styled.div<{ $alignItems?: 'flex-start' | 'flex-end' }>`
  display: flex;
  justify-content: space-between;
  align-items: ${props => props.$alignItems || 'flex-end'};
  width: 100%;
`;

export const OverlayTopLeft = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const OverlayTopRight = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const OverlayBottomLeft = styled.div`
  display: flex;
  align-items: flex-end;
`;

export const OverlayBottomRight = styled.div`
  display: flex;
  align-items: flex-end;
`;
