import React from 'react';
import {
  OverlayTopSection,
  OverlayBottomSection,
  OverlayTopLeft,
  OverlayTopRight,
  OverlayBottomLeft,
  OverlayBottomRight,
} from './bannerOverlayContent.style';

interface BannerOverlayContentProps {
  topLeft?: React.ReactNode;
  topRight?: React.ReactNode;
  bottomLeft?: React.ReactNode;
  bottomRight?: React.ReactNode;
  bottomAlignItems?: 'flex-start' | 'flex-end';
}

const BannerOverlayContent: React.FC<BannerOverlayContentProps> = ({
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  bottomAlignItems = 'flex-end',
}) => {
  return (
    <>
      <OverlayTopSection>
        <OverlayTopLeft>{topLeft}</OverlayTopLeft>
        <OverlayTopRight>{topRight}</OverlayTopRight>
      </OverlayTopSection>
      <OverlayBottomSection $alignItems={bottomAlignItems}>
        <OverlayBottomLeft>{bottomLeft}</OverlayBottomLeft>
        <OverlayBottomRight>{bottomRight}</OverlayBottomRight>
      </OverlayBottomSection>
    </>
  );
};

export default BannerOverlayContent;
