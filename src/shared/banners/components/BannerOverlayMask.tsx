import React from 'react';
import {
  OverlayMaskContainer,
  LeftOverlaySection,
  BottomOverlaySection,
  LeftMask,
  BottomMask,
} from './bannerOverlayMask.style';

interface BannerOverlayMaskProps {
  leftContent?: React.ReactNode;
  bottomContent?: React.ReactNode;
  showLeftOverlay?: boolean;
  showBottomOverlay?: boolean;
  showMask?: boolean;
}

const BannerOverlayMask: React.FC<BannerOverlayMaskProps> = () => {
  return (
    <OverlayMaskContainer>
      <LeftOverlaySection>
        <LeftMask />
      </LeftOverlaySection>

      <BottomOverlaySection>
        <BottomMask />
      </BottomOverlaySection>
    </OverlayMaskContainer>
  );
};

export default BannerOverlayMask;
