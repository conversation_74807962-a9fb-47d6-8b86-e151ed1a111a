import styled from 'styled-components';
import { AppColors } from 'styles/theme/colors';
import { applyOpacity } from 'utils/styleUtils';

export const OverlayMaskContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
`;

export const LeftOverlaySection = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 45%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 48px 32px;
  overflow: hidden;
  pointer-events: auto;
`;

export const BottomOverlaySection = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: center;
  padding: 32px 48px;
  overflow: hidden;
  pointer-events: auto;
`;

export const LeftMask = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    ${applyOpacity(AppColors.darkNavy, 0.8)} 0%,
    ${applyOpacity(AppColors.darkNavy, 0.7)} 40%,
    ${applyOpacity(AppColors.darkNavy, 0.5)} 60%,
    ${applyOpacity(AppColors.darkNavy, 0.3)} 75%,
    ${applyOpacity(AppColors.darkNavy, 0.2)} 85%,
    ${applyOpacity(AppColors.darkNavy, 0.1)} 95%,
    transparent 100%
  );
  z-index: -1;
`;

export const BottomMask = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to top,
    ${applyOpacity(AppColors.darkNavy, 0.8)} 0%,
    ${applyOpacity(AppColors.darkNavy, 0.7)} 40%,
    ${applyOpacity(AppColors.darkNavy, 0.5)} 60%,
    ${applyOpacity(AppColors.darkNavy, 0.3)} 75%,
    ${applyOpacity(AppColors.darkNavy, 0.2)} 85%,
    ${applyOpacity(AppColors.darkNavy, 0.1)} 95%,
    transparent 100%
  );
  z-index: -1;
`;
