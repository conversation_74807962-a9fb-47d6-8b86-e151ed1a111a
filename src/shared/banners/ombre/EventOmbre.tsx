import React from 'react';
import { IoBookmark, IoEye } from 'react-icons/io5';

import VerticalSeparator from 'shared/separators/VerticalSeparator';
import { formatDateTime } from 'utils/dateUtils';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import {
  GameLogo,
  GameLogoContainer,
  GameOmbreContainer,
  StatItem,
  StatsContainer,
  StatText,
  TitleText,
} from './GameOmbre.style';

interface EventOmbreProps {
  gameLogoUrl: string;
  eventTitle?: string;
  stats?: {
    dropDate?: string;
    expires?: string;
    views?: number;
    saves?: number;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

const EventOmbre: React.FC<EventOmbreProps> = ({
  gameLogoUrl,
  eventTitle = 'Season 3 Battlepass',
  stats = {
    dropDate: '',
    expires: '',
    views: 0,
    saves: 0,
  },
}) => {
  return (
    <GameOmbreContainer>
      {/* Game Logo or Event Title Section */}
      {gameLogoUrl ? (
        <GameLogoContainer>
          <GameLogo src={gameLogoUrl} alt={eventTitle} />
        </GameLogoContainer>
      ) : null}

      <TitleText>{eventTitle}</TitleText>

      {/* Stats Section */}
      <StatsContainer>
        <StatText>Drop Date: {formatDateTime(stats?.dropDate ?? '')}</StatText>
        {stats?.expires && (
          <>
            <VerticalSeparator height="24px" />
            <StatText>Expires: {formatDateTime(stats.expires)}</StatText>
          </>
        )}
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoEye size={32} />
          <StatText>{formatNumberWithSuffix(stats.views ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoBookmark size={32} />
          <StatText>{formatNumberWithSuffix(stats.saves ?? 0)}</StatText>
        </StatItem>
      </StatsContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          icon={<FaLightbulb size={40} />}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More Game Info
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoBookmarkOutline size={40} />}
          onClick={onSaveClick}
          focusKey="save-button"
        >
          Save
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default EventOmbre;
