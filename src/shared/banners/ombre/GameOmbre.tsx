import React from 'react';
import { FaCircle } from 'react-icons/fa';
import { IoGameController, IoHeart, IoPeople } from 'react-icons/io5';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import {
  GameLogo,
  GameLogoContainer,
  GameOmbreContainer,
  GenreItem,
  GenresContainer,
  RatingText,
  StatIcon,
  StatItem,
  StatsContainer,
  StatText,
  TitleText,
} from './GameOmbre.style';

interface GameOmbreProps {
  gameLogoUrl: string;
  gameTitle?: string;
  stats?: {
    players?: number;
    followers?: number;
    likes?: number;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

const GameOmbre: React.FC<GameOmbreProps> = ({
  gameLogoUrl,
  gameTitle = 'Game Title',
  stats = {
    players: 0,
    followers: 0,
    likes: 0,
    rating: 'E for Everyone',
  },
  genres = [],
}) => {
  return (
    <GameOmbreContainer>
      {/* Game Logo or Title Section */}
      {gameLogoUrl ? (
        <GameLogoContainer>
          <GameLogo src={gameLogoUrl} alt={gameTitle} />
        </GameLogoContainer>
      ) : (
        <TitleText>{gameTitle}</TitleText>
      )}

      {/* Stats Section */}
      <StatsContainer>
        <StatItem>
          <StatIcon>
            <IoGameController size={32} />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats.players ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <StatIcon>
            <IoPeople size={32} />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats.followers ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <StatItem>
          <StatIcon>
            <IoHeart size={32} />
          </StatIcon>
          <StatText>{formatNumberWithSuffix(stats.likes ?? 0)}</StatText>
        </StatItem>
        <VerticalSeparator height="24px" />
        <RatingText>{stats.rating}</RatingText>
      </StatsContainer>

      {/* Genres Section */}
      <GenresContainer>
        {genres.map((genre, index) => (
          <React.Fragment key={index}>
            <GenreItem>{genre}</GenreItem>
            {index < genres.length - 1 && <FaCircle size={16} />}
          </React.Fragment>
        ))}
      </GenresContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoGameController size={40} />}
          onClick={onPlayClick}
          focusKey="play-button"
        >
          Play
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<FaLightbulb size={40} />}
          onClick={onMoreInfoClick}
          focusKey="more-info-button"
        >
          More info
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default GameOmbre;
