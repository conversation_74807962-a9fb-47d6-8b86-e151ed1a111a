import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { useState } from 'react';
import MediaTileBadge from 'shared/badges/mediaTileBadge/MediaTIleBadge';
import { ContentType } from 'types/focused-card.types';
import { formatDuration } from 'utils/helpers.utils';

import BannerOverlayContent from '../components/BannerOverlayContent';
import EventOmbre from './EventOmbre';
import GameOmbre from './GameOmbre';
import VideoOmbre from './VideoOmbre';
import { BannerType } from 'enums/banner-type.enums';
import { BannerLayout } from '../bannerLayout';

interface GameData {
  gameLogoUrl?: string;
  logoUrl?: string;
  gameTitle?: string;
  title?: string;
  bannerUrl?: string;
  contentType?: ContentType;
  stats?: {
    players?: number;
    followers?: number;
    likes?: number;
    rating?: string;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

interface VideoData {
  gameLogoUrl?: string | null;
  logoUrl?: string;
  gameTitle?: string;
  title?: string;
  bannerUrl?: string;
  contentType?: ContentType;
  uploadDate?: string;
  duration?: number; // Duration in seconds from API
  publisherInfo?: {
    imageUrl?: string;
    publisherName?: string;
    isVerified?: boolean;
  };
  stats?: {
    uploadTime?: string;
    views?: number;
    likes?: number;
  };
  onPlayClick?: () => void;
  onSaveClick?: () => void;
}

interface EventData {
  gameLogoUrl?: string | null;
  logoUrl?: string;
  eventTitle?: string;
  title?: string;
  bannerUrl?: string;
  contentType?: ContentType;
  stats?: {
    dropDate?: string;
    expires?: string;
    views?: number;
    saves?: number;
  };
  onMoreInfoClick?: () => void;
  onSaveClick?: () => void;
}

type ContentData = GameData | VideoData | EventData;

interface LibraryOmbreProps<T extends ContentData = ContentData> {
  data: T;
  contentType?: ContentType; // Made optional since we'll use data.contentType
  onFocus?: (layout: { x: number; y: number; width: number; height: number }) => void;
  bannerType?: BannerType;
}

const LibraryOmbre = <T extends ContentData>({
  data,
  contentType,
  onFocus,
  bannerType = BannerType.OMBRE,
}: LibraryOmbreProps<T>) => {
  const [isFocused, setIsFocused] = useState(false);

  const { ref, focusKey } = useFocusable({
    focusable: true,
    trackChildren: true,
    autoRestoreFocus: true,
    preferredChildFocusKey: getPreferredChildFocusKey(
      data.contentType || contentType || ContentType.GAME
    ),
    onFocus: layout => {
      setIsFocused(true);
      onFocus?.(layout);
    },
    onBlur: () => {
      setIsFocused(false);
    },
  });

  function getPreferredChildFocusKey(type: ContentType): string {
    switch (type) {
      case ContentType.GAME:
        return 'play-button';
      case ContentType.VIDEO:
        return 'play-button';
      case ContentType.EVENT:
        return 'more-info-button';
      default:
        return 'play-button';
    }
  }

  const renderOmbre = () => {
    const currentContentType = data.contentType || contentType || ContentType.GAME;
    switch (currentContentType) {
      case ContentType.GAME: {
        const gameData = data as GameData;
        return (
          <BannerOverlayContent
            bottomLeft={
              <GameOmbre
                gameLogoUrl={gameData.gameLogoUrl ?? ''}
                gameTitle={gameData.gameTitle || gameData.title}
                stats={gameData.stats}
                genres={gameData.genres?.slice(0, 5)}
                onPlayClick={gameData.onPlayClick}
                onMoreInfoClick={gameData.onMoreInfoClick}
              />
            }
          />
        );
      }
      case ContentType.VIDEO: {
        const videoData = data as VideoData;
        return (
          <BannerOverlayContent
            topLeft={<MediaTileBadge>New Trailer</MediaTileBadge>}
            bottomRight={
              videoData.duration ? (
                <MediaTileBadge showBorder={false}>
                  {formatDuration(videoData.duration, true)}
                </MediaTileBadge>
              ) : undefined
            }
            bottomLeft={
              <VideoOmbre
                gameTitle={videoData.gameTitle || videoData.title}
                uploadDate={videoData.uploadDate}
                publisherInfo={videoData.publisherInfo}
                stats={videoData.stats}
                onPlayClick={videoData.onPlayClick}
                onSaveClick={videoData.onSaveClick}
              />
            }
          />
        );
      }
      case ContentType.EVENT: {
        const eventData = data as EventData;
        return (
          <BannerOverlayContent
            topLeft={<MediaTileBadge>New Event</MediaTileBadge>}
            bottomLeft={
              <EventOmbre
                gameLogoUrl={eventData.gameLogoUrl || eventData.logoUrl || ''}
                eventTitle={eventData.eventTitle || eventData.title}
                stats={eventData.stats}
                onMoreInfoClick={eventData.onMoreInfoClick}
                onSaveClick={eventData.onSaveClick}
              />
            }
          />
        );
      }

      case ContentType.OFFER: {
        const eventData = data as EventData;
        return (
          <BannerOverlayContent
            topLeft={<MediaTileBadge>New Offer</MediaTileBadge>}
            bottomLeft={
              <EventOmbre
                gameLogoUrl={eventData.gameLogoUrl || eventData.logoUrl || ''}
                eventTitle={eventData.eventTitle || eventData.title}
                stats={eventData.stats}
                onMoreInfoClick={eventData.onMoreInfoClick}
                onSaveClick={eventData.onSaveClick}
              />
            }
          />
        );
      }
      default:
        return null;
    }
  };

  return (
    <FocusContext.Provider value={focusKey}>
      <div
        ref={ref}
        // onMouseEnter={stopAutoPlay} onMouseLeave={startAutoPlay}
      >
        <BannerLayout bannerType={bannerType} backgroundImage={data?.bannerUrl} focused={isFocused}>
          {renderOmbre()}
        </BannerLayout>
      </div>
    </FocusContext.Provider>
  );
};

export default LibraryOmbre;
