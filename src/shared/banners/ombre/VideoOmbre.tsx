import React from 'react';
import { IoEye, IoHeart } from 'react-icons/io5';
import PublisherInfo from 'shared/publisherInfo';
import VerticalSeparator from 'shared/separators/VerticalSeparator';
import { DisplayLG } from 'styles/theme/typography';
import { getTimeRemaining } from 'utils/dateUtils';
import { formatNumberWithSuffix } from 'utils/numberUtils';
import { GameOmbreContainer, StatItem, StatsContainer, StatText } from './GameOmbre.style';

interface VideoOmbreProps {
  gameTitle?: string;
  uploadDate?: string; // ISO date string for when the video was uploaded
  publisherInfo?: {
    imageUrl?: string;
    publisherName?: string;
    isVerified?: boolean;
  };
  stats?: {
    uploadTime?: string;
    views?: number;
    likes?: number;
  };
  onPlayClick?: () => void;
  onSaveClick?: () => void;
}

const VideoOmbre: React.FC<VideoOmbreProps> = ({
  gameTitle = 'Video Title',
  uploadDate,
  publisherInfo,
  stats = {
    uploadTime: 'Uploaded: 2 Hours Ago',
    views: 0,
    likes: 0,
  },
}) => {
  // Use getTimeRemaining if uploadDate is provided, otherwise use stats.uploadTime
  const displayUploadTime = uploadDate
    ? `Uploaded: ${getTimeRemaining(uploadDate)}`
    : stats.uploadTime;
  return (
    <GameOmbreContainer>
      {/* Video Title Section */}
      <DisplayLG>{gameTitle}</DisplayLG>

      <PublisherInfo
        imageUrl={publisherInfo?.imageUrl ?? ''}
        publisherName={publisherInfo?.publisherName ?? 'Publisher'}
        profileSize={80}
        iconSize={40}
        isVerified={publisherInfo?.isVerified}
      />
      {/* Stats Section */}
      <StatsContainer>
        <StatText>{displayUploadTime}</StatText>
        <VerticalSeparator height="24px" />
        <IoEye size={32} />
        <StatText>{formatNumberWithSuffix(stats.views ?? 0)}</StatText>
        <VerticalSeparator height="24px" />
        <StatItem>
          <IoHeart size={32} />
          <StatText>{formatNumberWithSuffix(stats.likes ?? 0)}</StatText>
        </StatItem>
      </StatsContainer>

      {/* Buttons Section */}
      {/* <ButtonsContainer>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoPlay size={40} />}
          onClick={onPlayClick}
          focusKey="play-button"
        >
          Watch
        </PrimaryButton>
        <PrimaryButton
          variant="hero"
          width="270px"
          icon={<IoBookmarkOutline size={40} />}
          onClick={onSaveClick}
          focusKey="save-button"
        >
          Save
        </PrimaryButton>
      </ButtonsContainer> */}
    </GameOmbreContainer>
  );
};

export default VideoOmbre;
