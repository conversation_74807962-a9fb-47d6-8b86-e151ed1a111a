import styled from 'styled-components';
import { BodyXL, DisplayLG } from 'styles/theme/typography';

export const GameOmbreContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 24px;
`;

export const GameLogoContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

export const GameLogo = styled.img`
  max-width: 500px;
  max-height: 200px;
  object-fit: contain;
`;

export const StatsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
`;

export const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

export const StatIcon = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.iconWhite};

  svg {
    color: inherit;
  }
`;

export const TitleText = styled(DisplayLG)`
  /* Text/Body/LG */
  text-align: left;
`;
export const StatText = styled(BodyXL)`
  /* Text/Body/LG */
`;

export const RatingText = styled(BodyXL)`
  /* Text/Body/LG */
`;

export const GenresContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
`;

export const GenreItem = styled(BodyXL)`
  /* Text/Body/LG */
`;

export const ButtonsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 8px;
`;
