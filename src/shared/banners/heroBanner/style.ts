import { BannerType } from 'enums/banner-type.enums';
import styled from 'styled-components';

interface BannerStyledProps {
  $bannerType: BannerType;
  $focused?: boolean;
}

interface SlideStyledProps extends BannerStyledProps {
  $isActive: boolean;
}

interface NavigationStyledProps extends BannerStyledProps {
  $isActive?: boolean;
  $focused?: boolean;
}

export const HeroBannerContainer = styled.div<BannerStyledProps>`
  width: 100%;
  height: ${({ $focused }) => ($focused ? '800px' : '640px')};
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme.bgColor};
  border-radius: ${({ $bannerType }) => ($bannerType === BannerType.DYNAMIC ? '0' : '8px')};
  border: ${({ $focused, theme }) =>
    $focused ? `2px solid ${theme.outlineStrokeFocusWhite || '#FFFFFF'}` : 'none'};
  box-sizing: border-box;

  &:focus-within {
    outline: 2px solid ${({ theme }) => theme.outlineStrokeFocusWhite};
    outline-offset: 2px;
  }
`;

export const HeroBannerWrapper = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
`;

export const SlideContainer = styled.div`
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
`;

export const SlideContent = styled.div<SlideStyledProps>`
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: ${({ $isActive }) => ($isActive ? 1 : 0)};
  visibility: ${({ $isActive }) => ($isActive ? 'visible' : 'hidden')};
  transition:
    opacity 0.5s ease-in-out,
    visibility 0.5s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;

  padding: 0;
`;

export const NavigationDots = styled.div<BannerStyledProps>`
  position: absolute;
  bottom: 100px;
  right: 54px;
  display: flex;
  gap: 24px;
  z-index: 10;
`;

export const NavigationDot = styled.div<NavigationStyledProps>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  outline: 3px solid
    ${({ $focused, theme }) => ($focused ? theme.outlineStrokeFocusTertiary : 'transparent')};
  background: ${({ $focused, $isActive, theme }) =>
    $isActive || $focused ? theme.surfaceWhite : theme.surfaceSecondaryOpacity60};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: ${({ theme }) => theme.surfaceWhite};
    transform: scale(1.1);
  }

  &:focus {
    border-color: ${({ theme }) => theme.outlineStrokeFocusTertiary};
    outline: none;
  }
`;

export const ImagePreview = styled.img<NavigationStyledProps>`
  width: 116px;
  height: 72px;
  aspect-ratio: 29/18;
  border-radius: 4px;
  outline: 3px solid
    ${({ $isActive, $focused, theme }) =>
      $isActive || $focused ? theme.outlineStrokeFocusTertiary : 'transparent'};
  outline-offset: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  object-fit: cover;
  transform: ${({ $focused }) => ($focused ? 'scale(1.1)' : 'scale(1)')};

  &:hover {
    border-color: ${({ theme }) => theme.outlineStrokeFocusWhite};
    transform: scale(1.05);
  }

  &:focus {
    outline: 3px solid ${({ theme }) => theme.outlineStrokeFocusWhite};
    outline-offset: 2px;
  }
`;
