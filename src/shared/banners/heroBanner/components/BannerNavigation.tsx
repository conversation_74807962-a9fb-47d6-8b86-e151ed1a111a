import React from 'react';
import { NavigationDots } from '../style';

import NavigationDotWrapper from './NavigationDotWrapper';
import ImagePreviewWrapper from './ImagePreviewWrapper';
import { BannerType } from 'enums/banner-type.enums';

interface BannerNavigationProps<T extends { imageUrl?: string }> {
  data: T[];
  bannerType: BannerType;
  currentSlide: number;
  onSlideSelect: (index: number) => void;
  onFocus?: () => void;
}

const BannerNavigation = <T extends { imageUrl?: string }>({
  data,
  bannerType,
  currentSlide,
  onSlideSelect,
  onFocus,
}: BannerNavigationProps<T>) => {
  if (data.length <= 1) return null;

  return (
    <NavigationDots $bannerType={bannerType}>
      {bannerType === BannerType.DYNAMIC
        ? // Show dots for dynamic banner
          data.map((_, index) => (
            <NavigationDotWrapper
              key={index}
              index={index}
              isActive={index === currentSlide}
              bannerType={bannerType}
              onSelect={() => onSlideSelect(index)}
            />
          ))
        : // Show image previews for standard banner
          data.map((item, index) => (
            <ImagePreviewWrapper
              key={index}
              index={index}
              isActive={index === currentSlide}
              bannerType={bannerType}
              imageUrl={item.imageUrl ?? 'https://picsum.photos/seed/picsum/60/40'}
              onSelect={() => onSlideSelect(index)}
              onFocus={onFocus}
            />
          ))}
    </NavigationDots>
  );
};

export default BannerNavigation;
