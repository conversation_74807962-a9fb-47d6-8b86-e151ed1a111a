import React from 'react';
import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { NavigationDot } from '../style';
import { BannerType } from 'enums/banner-type.enums';

interface NavigationDotWrapperProps {
  index: number;
  isActive: boolean;
  bannerType: BannerType;
  onSelect: () => void;
}

const NavigationDotWrapper: React.FC<NavigationDotWrapperProps> = ({
  index,
  isActive,
  bannerType,
  onSelect,
}) => {
  const { ref, focused } = useFocusable({
    onEnterPress: onSelect,
    focusKey: `nav-dot-${index}`,
  });

  return (
    <NavigationDot
      ref={ref}
      $isActive={isActive}
      $focused={focused}
      $bannerType={bannerType}
      onClick={onSelect}
      role="button"
      tabIndex={0}
      aria-label={`Go to slide ${index + 1}`}
    />
  );
};

export default NavigationDotWrapper;
