import { useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import React from 'react';

import { ImagePreview } from '../style';
import { BannerType } from 'enums/banner-type.enums';

interface ImagePreviewWrapperProps {
  index: number;
  isActive: boolean;
  bannerType: BannerType;
  imageUrl: string;
  onSelect: () => void;
  onFocus?: () => void;
}

const ImagePreviewWrapper: React.FC<ImagePreviewWrapperProps> = ({
  index,
  isActive,
  bannerType,
  imageUrl,
  onSelect,
  onFocus,
}) => {
  const { ref, focused } = useFocusable({
    onEnterPress: onSelect,
    focusKey: `nav-preview-${index}`,
    onFocus: () => {
      if (onFocus) {
        onFocus();
      }
    },
  });

  const placeholderUrl = 'https://picsum.photos/seed/picsum/60/40';

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    const target = event.target as HTMLImageElement;
    target.src = placeholderUrl; // Set alternative image source
  };

  return (
    <ImagePreview
      ref={ref}
      $isActive={isActive || focused}
      $bannerType={bannerType}
      src={imageUrl}
      alt={`Preview ${index + 1}`}
      onClick={onSelect}
      role="button"
      tabIndex={0}
      aria-label={`Go to slide ${index + 1}`}
      $focused={focused}
      onError={handleImageError}
    />
  );
};

export default ImagePreviewWrapper;
