import React from 'react';

import { BannerType } from 'enums/banner-type.enums';
import { SlideContainer, SlideContent } from '../style';
import { BannerLayout } from 'shared/banners/bannerLayout';

interface BannerSlidesProps<T extends { imageUrl?: string }> {
  data: T[];
  currentSlide: number;
  bannerType: BannerType;
  renderSlide: (item: T, index: number) => React.ReactNode;
}

const BannerSlides = <T extends { imageUrl?: string }>({
  data,
  currentSlide,
  bannerType,
  renderSlide,
}: BannerSlidesProps<T>) => {
  return (
    <SlideContainer>
      {data.map((item, index) => (
        <SlideContent key={index} $isActive={index === currentSlide} $bannerType={bannerType}>
          <BannerLayout backgroundImage={item.imageUrl}>{renderSlide(item, index)}</BannerLayout>
        </SlideContent>
      ))}
    </SlideContainer>
  );
};

export default BannerSlides;
