import { useCallback, useRef, useEffect } from 'react';

interface UseAutoPlayProps {
  autoPlay: boolean;
  dataLength: number;
  autoPlayInterval: number;
  nextSlide: () => void;
}

export const useAutoPlay = ({
  autoPlay,
  dataLength,
  autoPlayInterval,
  nextSlide,
}: UseAutoPlayProps) => {
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  const startAutoPlay = useCallback(() => {
    if (autoPlay && dataLength > 1) {
      autoPlayRef.current = setInterval(nextSlide, autoPlayInterval);
    }
  }, [autoPlay, dataLength, nextSlide, autoPlayInterval]);

  const stopAutoPlay = useCallback(() => {
    if (autoPlayRef.current) {
      clearInterval(autoPlayRef.current);
      autoPlayRef.current = null;
    }
  }, []);

  useEffect(() => {
    startAutoPlay();
    return () => stopAutoPlay();
  }, [startAutoPlay, stopAutoPlay]);

  return {
    startAutoPlay,
    stopAutoPlay,
  };
};
