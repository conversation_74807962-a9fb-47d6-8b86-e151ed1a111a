import {
  useFocusable,
  type FocusableComponentLayout,
  type FocusDetails,
} from '@noriginmedia/norigin-spatial-navigation';
import React, { useCallback, useEffect, useState } from 'react';
import BannerNavigation from './components/BannerNavigation';
import BannerSlides from './components/BannerSlides';
import { useAutoPlay } from './hooks/useAutoPlay';
import { HeroBannerContainer, HeroBannerWrapper } from './style';
import { BannerType } from 'enums/banner-type.enums';

/**
 * Banner type options
 */

/**
 * Props interface for the HeroBanner component
 * @template T - The type of data items in the banner
 */
interface HeroBannerProps<T extends { imageUrl?: string }> {
  /** Array of data objects to display as slides */
  data: T[];
  /** Function to render each slide with the data item */
  renderSlide: (item: T, index: number) => React.ReactNode;
  /** Banner type - dynamic or standard */
  bannerType: BannerType;
  /** Auto-play interval in milliseconds (optional) */
  autoPlayInterval?: number;
  /** Whether to show navigation dots (default: true) */
  showNavigation?: boolean;
  /** Whether to enable auto-play (default: false) */
  autoPlay?: boolean;
  /** Callback when banner gains focus */
  onFocus?: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  /** Callback when slide changes */
  onSlideChange?: (currentIndex: number, item: T) => void;
}

/**
 * HeroBanner Component
 *
 * A customizable hero banner component that displays slides with navigation.
 * Supports both dynamic and standard banner types with focus-based navigation.
 *
 * Features:
 * - Configurable banner types (dynamic/standard)
 * - Custom slide rendering via callback
 * - Auto-play functionality
 * - Navigation dots/image previews
 * - Focus-based navigation using spatial navigation
 * - Smooth slide transitions
 *
 * @template T - The type of data items displayed in the banner
 *
 * @example
 * // Basic usage with game data
 * <HeroBanner
 *   data={gamesList}
 *   bannerType="dynamic"
 *   renderSlide={(game, index) => <GameSlide key={index} game={game} />}
 *   onSlideChange={(index, game) => console.log('Current game:', game)}
 * />
 *
 * @example
 * // With auto-play and custom interval
 * <HeroBanner
 *   data={promoData}
 *   bannerType="standard"
 *   renderSlide={(promo, index) => <PromoSlide key={index} promo={promo} />}
 *   autoPlay={true}
 *   autoPlayInterval={5000}
 *   showNavigation={true}
 * />
 */
const HeroBanner = <T extends { imageUrl?: string }>({
  data,
  renderSlide,
  bannerType,
  autoPlayInterval = 4000,
  showNavigation = true,
  autoPlay = true,
  onFocus,
  onSlideChange,
}: HeroBannerProps<T>) => {
  /** Current active slide index */
  const [currentSlide, setCurrentSlide] = useState(0);

  const { ref, focused } = useFocusable({
    focusKey: `hero-banner-${bannerType}`,
    onFocus,
    saveLastFocusedChild: true,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
    // onArrowPress: direction => {
    //   if (direction === 'down') {
    //     return false; // Allow navigation down to carousel rows
    //   }
    //   return true; // Handle other directions normally
    // },
  });

  /**
   * Moves to the next slide
   */
  const nextSlide = useCallback(() => {
    if (data.length === 0) return;

    const newIndex = (currentSlide + 1) % data.length;
    setCurrentSlide(newIndex);

    if (onSlideChange) {
      onSlideChange(newIndex, data[newIndex]);
    }
  }, [currentSlide, data, onSlideChange]);

  /**
   * Jumps to a specific slide
   */
  const goToSlide = useCallback(
    (index: number) => {
      if (index >= 0 && index < data.length) {
        setCurrentSlide(index);

        if (onSlideChange) {
          onSlideChange(index, data[index]);
        }
      }
    },
    [data, onSlideChange]
  );

  // Auto-play functionality
  const { startAutoPlay, stopAutoPlay } = useAutoPlay({
    autoPlay,
    dataLength: data.length,
    autoPlayInterval,
    nextSlide,
  });

  /**
   * Effect to reset slide when data changes
   */
  useEffect(() => {
    if (data.length > 0 && currentSlide >= data.length) {
      setCurrentSlide(0);
    }
  }, [data.length, currentSlide]);

  // Return null if no data
  if (!data || data.length === 0) {
    return null;
  }

  return (
    <HeroBannerContainer
      ref={ref}
      $bannerType={bannerType}
      $focused={focused}
      onMouseEnter={stopAutoPlay}
      onMouseLeave={startAutoPlay}
    >
      <HeroBannerWrapper>
        <BannerSlides
          data={data}
          currentSlide={currentSlide}
          bannerType={bannerType}
          renderSlide={renderSlide}
        />

        {showNavigation && (
          <BannerNavigation
            data={data}
            bannerType={bannerType}
            currentSlide={currentSlide}
            onSlideSelect={goToSlide}
          />
        )}
      </HeroBannerWrapper>
    </HeroBannerContainer>
  );
};

export default HeroBanner;
