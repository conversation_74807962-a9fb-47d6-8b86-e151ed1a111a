import {
  FocusableComponentLayout,
  FocusDetails,
  useFocusable,
  setFocus,
} from '@noriginmedia/norigin-spatial-navigation';
import {
  addSavedEvent,
  addSavedOffer,
  removeSavedEvent,
  removeSavedOffer,
} from 'api/game-service/game-service.api';
import verifiedIcon from 'assets/icons/verified-icon.svg';
import { useState } from 'react';
import { CiBookmark } from 'react-icons/ci';
import { IoBookmark } from 'react-icons/io5';
import { PrimaryButton } from 'shared/buttons';
import { CardBox } from 'shared/card-box/CardBox';
import { CardOverlay } from 'shared/card-overlay/CardOverlay';
import { LabelMD } from 'styles/theme/typography';
import { handleLogoError } from 'utils/helpers.utils';
import { ActionBar, EventWrapper, ImgContainer, SubContainer, VerifiedIcon } from './style';

interface EventCardProps {
  imageUrl: string;
  isVerified: boolean;
  companyUrl: string;
  companyName: string;
  initialIsSaved?: boolean;

  eventId?: string;
  offerId?: string;
  focusKey?: string; // Accept focus key from parent
  onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => void;
  onCardFocused?: (props: object) => void; // New callback for when focused
  onSaveToggle?: () => void; // Callback to trigger refetch in parent
}

export function PromoCard({
  imageUrl,
  initialIsSaved = false,
  focusKey, // Get the focus key from parent
  onFocus,
  isVerified,
  companyName,
  companyUrl,
  eventId,
  offerId,
  onCardFocused,
  onSaveToggle,
}: EventCardProps) {
  const [isSaved, setIsSaved] = useState(initialIsSaved);
  const [isSaveLoading, setIsSaveLoading] = useState(false);

  const toggleSave = async () => {
    if (isSaveLoading) return;

    try {
      setIsSaveLoading(true);

      if (eventId) {
        // Handle event save/unsave
        if (isSaved) {
          await removeSavedEvent(eventId);
        } else {
          await addSavedEvent(eventId);
        }
      } else if (offerId) {
        // Handle offer save/unsave
        if (isSaved) {
          await removeSavedOffer(offerId);
        } else {
          await addSavedOffer(offerId);
        }
      }

      // Update local state only after successful API call
      setIsSaved(prev => !prev);
      console.log(`${eventId ? 'Event' : 'Offer'} saved state is now: ${!isSaved}`);

      // Trigger refetch in parent component
      if (onSaveToggle) {
        onSaveToggle();
      }
    } catch (error) {
      console.error('Error toggling save state:', error);
      // Optionally show user-friendly error message
    } finally {
      setIsSaveLoading(false);
    }
  };

  const buttonFocusKey = `${focusKey}-button`;

  const focusButton = () => {
    setFocus(buttonFocusKey);
  };

  // Card focus handling
  const { ref: cardRef, focused: cardFocused } = useFocusable({
    focusKey, // Use the provided focus key
    onEnterPress: focusButton,
    onArrowPress: (direction: string) => {
      if (direction === 'down') {
        focusButton();
        return false; // Prevent default behavior
      }
      return true; // Allow default behavior for other directions
    },
    onFocus: (layout: FocusableComponentLayout, props: object, details: FocusDetails) => {
      // Call the original onFocus callback
      onFocus(layout, props, details);

      // Call the onCardFocused callback when focused
      if (onCardFocused) {
        onCardFocused(props);
      }
    },
    extraProps: { imageUrl, isVerified, companyName, companyUrl, initialIsSaved },
  });

  return (
    // The card ref is on the wrapper, button ref is on the button container
    <EventWrapper ref={cardRef}>
      <CardBox
        className="image-container"
        focused={cardFocused}
        imageUrl={imageUrl}
        width="544px"
        height="216px"
        borderRadius="4px"
        padding="0"
      >
        <SubContainer width="100%" gap="8px" align="center">
          <ImgContainer onError={handleLogoError} src={companyUrl} alt={companyName} />
          <LabelMD>{companyName}</LabelMD>
          {isVerified && <VerifiedIcon src={verifiedIcon} alt="verified-icon" />}
        </SubContainer>
        <CardOverlay focused={cardFocused} />
      </CardBox>
      <ActionBar>
        <PrimaryButton
          variant="promo"
          focusKey={buttonFocusKey}
          onClick={toggleSave}
          isLoading={isSaveLoading}
          icon={isSaved ? <IoBookmark size={22} /> : <CiBookmark size={22} />}
        >
          {isSaved ? 'Unsave' : 'Save'}
        </PrimaryButton>
      </ActionBar>
    </EventWrapper>
  );
}
