import styled from 'styled-components';
import { Row } from 'styles/sharedStyles';

export const EventWrapper = styled.div`
  display: flex;
  flex-direction: column;
  .image-container {
    position: relative;
  }
  /* The focus border from CardBox will frame the whole component */
`;

export const ActionBar = styled.div`
  width: 544px;
  height: 60px;

  display: flex;
  align-items: center;

  box-sizing: border-box;
`;
export const ImgContainer = styled.img`
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
`;
export const SubContainer = styled(Row)`
  position: absolute;
  bottom: 8px;
  left: 8px;
`;
export const VerifiedIcon = styled.img``;
