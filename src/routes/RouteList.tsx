import { GameInterstitial } from 'modules/game-interstitial';
import {
  componentsPath,
  friendsRequestListPath,
  gameDetailsPath,
  gameInterstitialPath,
  homePath,
  libraryPagePath,
  profileDetailsPath,
  profileRoute,
  pubGameFollowListPath,
  publisherDetailsPath,
} from './path';
import GameDetails from 'modules/game-details/GameDetails';
import Home from 'modules/home/<USER>';
import LibraryPage from 'modules/library/LibraryPage';
import { RouteObject } from 'react-router-dom';
import { Components } from 'modules/components/Components';
import { ProfileDetails } from 'modules/profile-details';
import { FriendPage } from 'modules/friend-page';
import { FollowPage } from 'modules/follow-page';
import { PublisherPage } from 'modules/publisherPage/PublisherPage';

export const routeList: RouteObject[] = [
  {
    path: homePath,
    element: <Home />,
  },
  {
    path: gameDetailsPath,
    element: <GameDetails />,
  },
  {
    path: profileDetailsPath,
    element: <ProfileDetails />,
  },
  {
    path: publisherDetailsPath,
    element: <PublisherPage />,
  },
  {
    path: profileRoute,
    element: <ProfileDetails />,
  },
  {
    path: gameInterstitialPath,
    element: <GameInterstitial />,
  },
  {
    path: libraryPagePath,
    element: <LibraryPage />,
  },
  {
    path: friendsRequestListPath,
    element: <FriendPage />,
  },
  {
    path: pubGameFollowListPath,
    element: <FollowPage />,
  },
  {
    path: componentsPath,
    element: <Components />,
  },
];
