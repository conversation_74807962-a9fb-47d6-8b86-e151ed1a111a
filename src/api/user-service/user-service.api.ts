import apiEndpoints from 'api/endpoints.api';
import { storage } from 'utils/storage.utils';
import { STORAGE } from 'enums/storage.enums';
import { ApiService } from 'utils/api-service.utils';
import { ILoginReq } from 'types/api/user/login.user.api.types';
import { IProfile } from 'types/api/user/profile.user.api.types';
import { TermsRes } from 'types/api/user/terms.user.api.types';
import { Paginated } from 'types/api/paginated.api';
import {
  IProfileStatsRes,
  IFriendStatsRes,
  IFriendItem,
} from 'types/api/user/friend.user.api.types';
import appEnv from 'config/env.config';
import { PAGE_LIMIT } from 'config/app.config';
import { IPubFollowing } from 'types/api/user/follow.user.api.types';

// Initialize services
const apiService = new ApiService(appEnv.userBaseUrl);

// Exported functions
export const loginUser = async (req: ILoginReq) => {
  try {
    const response = await apiService.post(apiEndpoints.logInUrl, req);

    storage.set(STORAGE.AUTH_TOKEN, response.data.jwt_token);
  } catch (e) {
    console.error('Error during login:', e);
    throw e;
  }
};

export const getUserMyDetails = async (): Promise<IProfile> => {
  try {
    const response = await apiService.get(apiEndpoints.profile);

    return response.data;
  } catch (e) {
    storage.remove(STORAGE.AUTH_TOKEN);
    throw e;
  }
};

export const getUserById = async (userId: string): Promise<IProfile> => {
  try {
    const response = await apiService.get(apiEndpoints.getUserDetails + userId);
    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getTermsAndConditions = async (contentType?: string): Promise<TermsRes> => {
  try {
    let url = apiEndpoints.getTAndC;

    // Attach content_type to URL if provided
    if (contentType) {
      url += `?content_type=${contentType}`;
    }

    const response = await apiService.get(url);

    return response.data;
  } catch (e) {
    console.error('Error fetching terms and conditions:', e);
    throw e;
  }
};

export const getFriendsList = async (
  search?: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IFriendItem>> => {
  try {
    const response = await apiService.get(apiEndpoints.getMyFriends, {
      page: page.toString(),
      limit: limit.toString(),
      search: search,
    });

    const res = response.data;

    return { count: res.total as number, data: res.data };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getFriendRequestList = async (
  search?: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IFriendItem>> => {
  try {
    const response = await apiService.get(apiEndpoints.getFriendRequestList, {
      page: page.toString(),
      limit: limit.toString(),
      search: search ?? '',
    });

    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const acceptFriendRequest = async (requestId: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.acceptFriendRequest.replace('{request_id}', requestId));
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const rejectFriendRequest = async (requestId: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.rejectFriendRequest.replace('{request_id}', requestId));
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getPlayerProfileStats = async (userId: string): Promise<IProfileStatsRes> => {
  try {
    const response = await apiService.get(
      apiEndpoints.getPlayerProfileStats.replace('{userid}', userId)
    );

    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const checkFriendStatus = async (userId: string): Promise<IFriendStatsRes> => {
  try {
    const response = await apiService.get(
      apiEndpoints.checkFriendStatus.replace('{other_user_id}', userId)
    );

    return response.data;
  } catch (e) {
    throw new Error(`Failed to get friend status: ${e}`);
  }
};

export const sendFriendRequest = async (userId: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.sendFriendRequest, {
      recipient_user_id: userId,
    });
  } catch (e) {
    throw new Error(`Invalid response format: ${e} ${userId}`);
  }
};

export const unFriendUser = async (userId: string): Promise<void> => {
  try {
    await apiService.delete(apiEndpoints.unfriend.replace('{friend_user_id}', userId));
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getPubFollowing = async (
  userId: string,
  search?: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IPubFollowing>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubFollowers, {
      page: page.toString(),
      limit: limit.toString(),
      user_id: userId,
      search,
    });

    return { count: response.data?.count as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format getPubFollowing: ${e}`);
  }
};

export const followPub = async (userId: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.followPub, { follow_id: userId });
  } catch (e) {
    throw new Error(`Invalid response format followPub: ${e}`);
  }
};

export const unFollowPub = async (userId: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.unFollowPub, { follow_id: userId });
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const checkIsPubFollowed = async (userId: string): Promise<{ isFollowing: boolean }> => {
  try {
    const response = await apiService.post(apiEndpoints.checkPubFollow, {
      follow_id: userId,
    });

    return { isFollowing: response.data.following as boolean };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};
