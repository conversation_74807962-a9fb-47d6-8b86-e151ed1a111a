import apiEndpoints from 'api/endpoints.api';
import { PAGE_LIMIT } from 'config/app.config';
import appEnv from 'config/env.config';
import { GAME_CONTENT } from 'enums/game-content.enums';
import { GAME_MEDIA } from 'enums/game-media.enums';
import { PAGE_ID } from 'enums/idle-page.enums';
import { PLAYER_PROFILE_SECTION } from 'enums/player-profile-section.enum';
import { IContent } from 'types/api/game/content.game.api.types';
import { IFavoriteGame, IGame } from 'types/api/game/details.game.api.types';
import { IDynamicHeroModel } from 'types/api/game/dynamic-hero.game.api.types';
import { IEventsOffers } from 'types/api/game/events-offers.game.api.types';
import { IGameFollow, ILikeFollowStatus } from 'types/api/game/follow.game.api.types';
import { IGameMedia } from 'types/api/game/game-media.game.api.types';
import { IGameItem, IGamePayload } from 'types/api/game/marketplace.game.api.types';
import { IPlayerProfileGame } from 'types/api/game/player-profile.game.api.types';
import { IPublisherGame } from 'types/api/game/publisher-game.game.api.types';
import { IPublisherGenre } from 'types/api/game/publisher-genre.game.api.types';
import { IPubStats } from 'types/api/game/publisher-stats.game.api.types';
import { IPublisherLatestUpdate } from 'types/api/game/publisher-updates.game.api.types';
import { IRecentHistory } from 'types/api/game/recent-history.game.api.types';
import { ISavedEvent } from 'types/api/game/saved-events.game.api.types';
import { ISavedOffer } from 'types/api/game/saved-offers.game.api.types';
import {
  IScreenSaverAnimationSetting,
  IScreenSaverSetting,
} from 'types/api/game/screensaver-settings.game.api.types';
import { IScreenSaver } from 'types/api/game/screensaver.game.api.types';
import { ITrackIdleScreen } from 'types/api/game/track-idle-screen.game.api.types';
import { IVideoItem } from 'types/api/game/video-item.game.api.types';
import {
  ITrendingVideo,
  IVideoByGenre,
  IVideoGenre,
  IVideoPlayerModel,
  IVideoPlayerRelatedVideo,
  IVideoSceneModel,
} from 'types/api/game/video-player.game.api.types';

import { Paginated } from 'types/api/paginated.api';
import { ApiService } from 'utils/api-service.utils';

// Initialize API service
const apiService = new ApiService(appEnv.gameBaseUrl);

// Game Details Functions
export const getGameDetails = async (gameSlug: string): Promise<IGame> => {
  try {
    const url = `${apiEndpoints.gameOverView}/${gameSlug}`;
    const response = await apiService.get(url);

    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format getGameDetails: ${e}`);
  }
};

export const getMarketplaceGames = async (
  page: number = 1,
  limit: number = PAGE_LIMIT,
  filters?: Partial<IGamePayload>
): Promise<Paginated<IGameItem>> => {
  try {
    const mergedStudioIds = [
      ...(filters?.studio_id ?? []),
      ...(filters?.developer_id ?? []),
      ...(filters?.publisher_id ?? []),
    ];

    const body = {
      ...filters,
      page,
      limit,
      publisher_id: mergedStudioIds.length > 0 ? mergedStudioIds : null,
      game_chain: [],
      network: 'WEB3',
      external_category: 'gaming',
    };

    const response = await apiService.post(apiEndpoints.getGameList, body);

    return { count: response.data.count as number, data: response.data.games };
  } catch (e) {
    throw new Error(`Failed to fetch marketplace games: ${e}`);
  }
};

// Game Following Functions
export const followGame = async (gameSlug: string): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.followGame, { game_slug: gameSlug });
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const favoriteSaveGame = async (
  gameSlug: string,
  favorite?: boolean,
  saved?: boolean
): Promise<void> => {
  try {
    const url = `${apiEndpoints.favoriteSavedGame}/`;
    await apiService.post(url, {
      game_slug: gameSlug,
      favorite,
      saved,
    });
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const unFollowGame = async (gameSlug: string): Promise<void> => {
  try {
    await apiService.delete(apiEndpoints.followGame, {
      game_slug: gameSlug,
    });
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const checkLikeFollowGameStatus = async (gameSlug: string): Promise<ILikeFollowStatus> => {
  try {
    const url = `${apiEndpoints.checkLikeFollowGame}/${gameSlug}`;
    const response = await apiService.get(url);

    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

// Favorite and Saved Games Functions
export const getFavoriteGames = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IFavoriteGame>> => {
  try {
    const response = await apiService.get(apiEndpoints.getFavoriteGames, {
      page,
      limit,
    });

    return { count: response.data.total, data: response.data.data };
  } catch (e) {
    throw new Error(`Error fetching favorite games: ${e}`);
  }
};

export const getSavedGames = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IFavoriteGame>> => {
  try {
    const url = `${apiEndpoints.getSavedGames}`;
    const response = await apiService.get(url, {
      page,
      limit,
    });

    return { count: response.data.total, data: response.data.data };
  } catch (e) {
    throw new Error(`Error fetching saved games: ${e}`);
  }
};

// Content Functions
export const getSavedContent = async (
  contentType: GAME_CONTENT,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IContent>> => {
  try {
    const url = `${apiEndpoints.getSavedContent}`;
    const response = await apiService.get(url, {
      page,
      limit,
      content_type: contentType,
    });

    return { count: response.data.total, data: response.data.data };
  } catch (e) {
    throw new Error(`Error fetching saved content: ${e}`);
  }
};

export const getFavoriteContent = async (
  contentType: GAME_CONTENT,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IContent>> => {
  try {
    const url = `${apiEndpoints.getFavoriteContent}`;
    const response = await apiService.get(url, {
      page,
      limit,
      content_type: contentType,
    });

    return { count: response.data.total, data: response.data.data };
  } catch (e) {
    throw new Error(`Error fetching favorite content: ${e}`);
  }
};

// Video Functions
export const getFavoriteVideos = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IVideoItem>> => {
  try {
    const url = `${apiEndpoints.getFavoriteContent}`;
    const response = await apiService.get(url, { page, limit });

    return {
      count: response.data.total,
      data: response.data.data,
    };
  } catch (e) {
    throw new Error(`Error fetching favorite videos: ${e}`);
  }
};

export const getSavedVideos = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IVideoItem>> => {
  try {
    const url = `${apiEndpoints.getSavedContent}`;
    const response = await apiService.get(url, {
      page,
      limit,
      content_type: GAME_CONTENT.VIDEO,
    });

    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Error fetching saved videos: ${e}`);
  }
};

// Player Profile Functions
export const getPlayerProfileSectionGames = async (
  sectionType: PLAYER_PROFILE_SECTION,
  userId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  search?: string
): Promise<Paginated<IPlayerProfileGame>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPlayerProfileSectionGames, {
      page: page.toString(),
      limit: limit.toString(),
      section: sectionType,
      user_id: userId,
      search,
    });

    return { count: response.data.total as number, data: response.data.data };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getRecentHistory = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IRecentHistory>> => {
  try {
    const url = `${apiEndpoints.getRecentHistory}`;
    const response = await apiService.get(url, { page, limit });

    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Error fetching recent history: ${e}`);
  }
};

// Events and Offers Functions
export const getSavedEvents = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<ISavedEvent>> => {
  try {
    const url = `${apiEndpoints.getSavedEvents}`;
    const response = await apiService.get(url, { page, limit });

    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Error fetching saved events: ${e}`);
  }
};

export const getSavedOffers = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<ISavedOffer>> => {
  try {
    const url = `${apiEndpoints.getSavedOffers}`;
    const response = await apiService.get(url, { page, limit });

    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Error fetching saved offers: ${e}`);
  }
};

export const addSavedEvent = async (eventId: string): Promise<void> => {
  try {
    const url = `${apiEndpoints.getSavedEvents}${eventId}`;
    await apiService.post(url);
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const removeSavedEvent = async (eventId: string): Promise<void> => {
  try {
    const url = `${apiEndpoints.getSavedEvents}${eventId}`;
    await apiService.delete(url);
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const addSavedOffer = async (offerId: string): Promise<void> => {
  try {
    const url = `${apiEndpoints.getSavedOffers}${offerId}`;
    await apiService.post(url);
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const removeSavedOffer = async (offerId: string): Promise<void> => {
  try {
    const url = `${apiEndpoints.getSavedOffers}${offerId}`;
    await apiService.delete(url);
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

// Screen Saver Functions
export const getScreenSaverSetting = async (screenId: PAGE_ID): Promise<IScreenSaverSetting> => {
  try {
    const response = await apiService.get(apiEndpoints.getScreenSaverSetting, {
      screen_name: screenId,
    });

    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format getScreenSaverSetting: ${e}`);
  }
};

export const getScreenSaverAnimationSetting = async (): Promise<IScreenSaverAnimationSetting> => {
  try {
    const response = await apiService.get(apiEndpoints.getScreenSaverAnimationSetting);

    return {
      duration: response?.data?.[0]?.duration_in_seconds,
      textFadeDuration: response?.data?.[1]?.duration_in_seconds,
    };
  } catch (e) {
    throw new Error(`Invalid response format getScreenSaverAnimationSetting: ${e}`);
  }
};

export const getScreenSavers = async (
  screenIds: number[] = []
): Promise<{ screensavers: IScreenSaver[]; isLoop: boolean }> => {
  try {
    const response = await apiService.post(apiEndpoints.getScreenSavers, {
      ids: screenIds,
    });

    return {
      screensavers: response.data?.data,
      isLoop: response.data?.is_loop as boolean,
    };
  } catch (e) {
    throw new Error(`Invalid response format getScreenSavers: ${e}`);
  }
};

// Publisher Functions
export const getPubGames = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  isFeatured: boolean = false
): Promise<Paginated<IPublisherGame>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubGames, {
      page: page.toString(),
      limit: limit.toString(),
      publisher_id: pubId,
      only_featured: isFeatured.toString(),
    });
    return { count: response.data.total as number, data: response.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format getPubGames: ${e}`);
  }
};

export const getPubGameGenre = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IPublisherGenre>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubGameGenre, {
      page: page.toString(),
      limit: limit.toString(),
      publisher_id: pubId,
    });

    return { count: response.data?.length, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format getPubGameGenre: ${e}`);
  }
};

export const getPubLatestUpdates = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IPublisherLatestUpdate>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubLatestUpdates.replace('{id}', pubId), {
      page: page.toString(),
      limit: limit.toString(),
    });

    return { count: response.data?.total, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getPubHero = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IDynamicHeroModel>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubHero.replace('{id}', pubId), {
      page: page.toString(),
      limit: limit.toString(),
    });

    return {
      count: response?.data?.total,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getGameBanner = async (
  gameSlug: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IDynamicHeroModel>> => {
  try {
    const response = await apiService.get(apiEndpoints.getGameBanner.replace('{id}', gameSlug), {
      page: page.toString(),
      limit: limit.toString(),
    });

    return {
      count: response?.data?.total,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format getGameBanner: ${e}`);
  }
};

export const getPubFollowerCount = async (
  pubId: string,
  userId: string
): Promise<{ count: number | string }> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubFollowerCount, {
      pub_id: pubId,
      user_id: userId,
    });

    return { count: response?.data?.friends_count };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getGameFollowers = async (
  search?: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IGameFollow>> => {
  try {
    const response = await apiService.get(apiEndpoints.getGameFollowers, {
      page: page.toString(),
      limit: limit.toString(),
      search,
    });

    return {
      count: response?.data?.total as number,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format getGameFollowers: ${e}`);
  }
};

export const getPublisherStats = async (userId: string): Promise<IPubStats> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubStats, {
      user_id: userId,
    });

    return response?.data;
  } catch (e) {
    throw new Error(`Invalid response format getPublisherStats: ${e}`);
  }
};

export const getPubEvents = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  record?: string
): Promise<Paginated<IEventsOffers>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubEvents, {
      page: page.toString(),
      limit: limit.toString(),
      record_id: record,
      pub_id: pubId,
    });

    return { count: response?.data?.total, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format getPubEvents: ${e}`);
  }
};

export const getPubOffers = async (
  pubId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  record?: string
): Promise<Paginated<IEventsOffers>> => {
  try {
    const response = await apiService.get(apiEndpoints.getPubOffers, {
      page: page.toString(),
      limit: limit.toString(),
      record_id: record,
      pub_id: pubId,
    });

    return { count: response?.data?.total, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format getPubEvents: ${e}`);
  }
};

export const trackIdleScreen = async (idleScreen: ITrackIdleScreen): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.trackIdleScreen, {
      ...idleScreen,
    });
  } catch (e) {
    throw new Error(`Invalid response format trackIdleScreen: ${e}`);
  }
};

// Game Media Functions
export const getGameMedia = async (
  gameSlug: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  mediaType?: GAME_MEDIA[]
): Promise<Paginated<IGameMedia>> => {
  try {
    const url = `${apiEndpoints.getGameMedia}`;
    const response = await apiService.get(url, {
      page,
      limit,
      game_slug: gameSlug,
      media_type: mediaType?.join(', '),
    });

    return { count: response.data?.total as number, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Error fetching game media: ${e}`);
  }
};

export const getGameScreenShots = async (
  gameSlug: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IGameMedia>> => {
  try {
    const url = `${apiEndpoints.games}/${gameSlug}/screen-shots`;
    const response = await apiService.get(url, { page, limit });

    return {
      count: response?.data?.length,
      data: response.data?.data,
    };
  } catch (e) {
    throw new Error(`Error fetching game media: ${e}`);
  }
};

export const saveFavoriteImage = async (
  imageId: string,
  saveImage?: boolean,
  favImage?: boolean
): Promise<void> => {
  try {
    await apiService.post(apiEndpoints.saveFavoriteImage, {
      favorite: favImage,
      saved: saveImage,
      id: imageId,
    });
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

// Video Player Functions
export const getVideoPlayerVideo = async (
  mediaId: string,
  excludedMediaIds?: string[],
  genreIds?: string[]
): Promise<IVideoPlayerModel> => {
  try {
    const response = await apiService.post(apiEndpoints.getVideoPlayerVideo, {
      data: {
        exclude_media_ids: excludedMediaIds ?? [],
        genre_ids: genreIds ?? [],
        media_id: mediaId,
      },
    });

    return response.data;
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getVideoScenes = async (mediaId: string): Promise<IVideoSceneModel> => {
  try {
    const response = await apiService.get(
      apiEndpoints.getVideoScene.replace('{media_id}', mediaId)
    );

    return response?.data;
  } catch (e) {
    throw new Error(`Failed to parse video scenes: ${e}`);
  }
};

export const getTrendingVideos = async (
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<ITrendingVideo>> => {
  try {
    const response = await apiService.post(apiEndpoints.getTrendingVideos, {
      limit,
      page,
    });

    return {
      count: response?.data?.total as number,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getVideosByGenre = async (
  page: number = 1,
  limit: number = PAGE_LIMIT,
  excludedMediaIds?: string[],
  genreIds?: string[]
): Promise<Paginated<IVideoByGenre>> => {
  try {
    const response = await apiService.post(apiEndpoints.getVideosByGenre, {
      limit,
      page,
      exclude_media_ids: excludedMediaIds ?? [],
      genre_ids: genreIds ?? [],
    });

    return {
      count: response?.data?.total as number,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getVideosGenre = async (
  mediaId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT
): Promise<Paginated<IVideoGenre>> => {
  try {
    const response = await apiService.post(apiEndpoints.getGenreByVideo, {
      limit,
      page,
      media_id: mediaId,
    });

    return { count: response?.data.total as number, data: response?.data?.data };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getRelatedVideos = async (
  mediaId: string,
  page: number = 1,
  limit: number = PAGE_LIMIT,
  genreIds?: string[]
): Promise<Paginated<IVideoPlayerRelatedVideo>> => {
  try {
    const response = await apiService.post(apiEndpoints.getRelatedVideos, {
      limit,
      page,
      media_id: mediaId,
      genre_ids: genreIds ?? [],
    });

    return {
      count: response?.data?.total as number,
      data: response?.data?.data,
    };
  } catch (e) {
    throw new Error(`Invalid response format: ${e}`);
  }
};

export const getUserStatusWebSocket = (): string => {
  const token = apiService.getAuthToken();

  if (token) {
    const wssUrl = `${appEnv.gameWebSocketBaseUrl}?token=${token}`;
    return wssUrl;
  } else {
    // throw new Error('User token not found');
    return '';
  }
};
