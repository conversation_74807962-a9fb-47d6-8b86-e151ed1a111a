const apiEndpoints = {
  patchPublisherDetails: 'api/v1/publisher/',
  signUpUrl: 'api/v1/users/signup',
  logInUrl: 'api/v1/login',
  googleUrl: 'api/v1/users/google',
  googleConnect: 'api/v1/users/me/connect-google',
  googleConnectPublisher: 'api/v1/publisher/me/connect-google',
  metaUrl: 'api/v1/users/meta',
  metaConnectPublisher: 'api/v1/publisher/me/connect-meta',
  verifyEmailUrl: 'api/v1/users/email-verification',
  simpleHashUrl: 'https://api.simplehash.com/',
  forgotPassword: 'api/v1/users/forget-password',
  changeForgotPassword: 'api/v1/reset-password',
  userWallets: 'api/v1/users/me/wallets',
  addWallet: 'api/v1/users/me/add-wallet',
  getUserDetails: 'api/v1/users/',
  patchUserDetails: 'api/v1/users/me/update-profile',
  getPreSignedS3: 'api/v1/users/upload/get-presigned-url',
  getPublicPreSignedS3: 'api/v1/get-public-presigned-url',
  getPublisherPreSignedS3: 'api/v1/publisher/upload/presigned-url',
  getLaunchpadPreSignedS3: 'api/v1/s3/pre-signed-urls',
  resendVerifyEmailUrl: 'api/v1/users/resend-email-verification',
  checkResetUrl: 'api/v1/users/check-reset-otp',
  profile: 'api/v1/users/me/profile',
  twoFA: 'api/v1/users/me/enable-2fa',
  logInUsingOtpUrl: 'api/v1/users/login-using-otp',
  getKbaUrl: 'api/v1/user/kba/question/all',
  postKbaUrl: 'api/v1/user/kba/question/',
  updatePasswordUrl: 'api/v1/users/me/update-password',
  setPasswordUrl: 'api/v1/users/me/set-password',
  updatePublisherPasswordUrl: 'api/v1/publisher/update-password',
  setPublisherPasswordUrl: 'api/v1/publisher/me/set-password',
  disable2FaRequestUrl: 'api/v1/users/me/disable-2fa-request',
  disable2FaUrl: 'api/v1/users/me/disable-2fa',
  notificationUpdateUrl: 'api/v1/users/me/update-notification-settings',
  notificationUrl: 'api/v1/users/me/get-notification-settings',
  notificationPublisherUrl: 'api/v1/publisher/me/get-notification-settings',
  notificationUpdatePublisherUrl: 'api/v1/publisher/me/update-notification-settings',
  getInterestedUrl: 'api/v1/interested-game/',
  getUserInterestUrl: 'api/v1/user/interested-game/',
  postUserInterestUrl: 'api/v1/user/interested-game/',
  checkPassword: 'api/v1/users/me/generate-update-email-request',
  checkPublisherPassword: 'api/v1/publisher/update-email-request',
  updateEmail: 'api/v1/users/me/update-email',
  updatePublisherEmail: 'api/v1/publisher/update-email',
  feedbackUrl: 'api/v1/feedback',
  notifications: 'api/v1/me/get-all-notifications',
  notificationsPublisher: 'api/v1/users/me/publisher/get-all-notifications',
  unReadNotifications: 'api/v1/me/get-unread-notifications',
  follow: 'api/v1/me/follow-user',
  unFollow: 'api/v1/me/unfollow-user',
  getFollowers: 'api/v1/me/get-followers',
  getFollowing: 'api/v1/me/get-followings',
  checkFollowingUrl: 'api/v1/me/check-follow',
  searchUser: 'api/v1/search-user',
  publisherSignUp: 'api/v1/publisher/auth/signup',
  publisherLogIn: 'api/v1/publisher/auth/login',
  publisherSendVerifyOtp: 'api/v1/publisher/auth/send-verfication-otp',
  publisherVerifyEmail: 'api/v1/publisher/auth/confirm-verfication-otp',
  publisherProfile: 'api/v1/publisher/',
  publisherApiKeys: 'api/v1/publisher/api-key/keys',
  createPublisherApiKeys: 'api/v1/publisher/api-key',
  editPublisherApiKeys: 'api/v1/publisher/edit-api-key-name',
  getCategories: 'api/v1/categories',
  getGameList: 'api/v1/games/category',
  publisherAssetDetails: 'api/v1/publisher/asset',
  getPublisherGames: 'api/v1/pub/game/me',
  getPublisherGameDetails: 'api/v1/publisher/game',
  getPublisherAssetsByGameSlug: 'api/v1/publisher/asset',
  getAttributesByGameSlug: 'api/v1/assets/attrs',
  getGameDetails: 'api/v1/games-detail',
  getGameScreenshots: 'api/v1/games',
  getAssetsByGameSlug: 'api/v1/game/assets',
  getAssetsByGameSlugV2: 'api/v1/game/assets-v2',
  getMyAssets: 'api/v1/user/asset/me',
  getAssetDetails: 'api/v1/assets',
  getAssetSolanaAuctionDetails: 'api/v1/listing',
  createEnglishAuction: 'api/v1/listing/english',
  bidEnglishAuction: 'api/v1/bid/english',
  getEthNonce: 'api/v1/user-nonce',
  getEthAuctionListings: 'api/v1/listing',
  getEthAuctionOffers: 'api/v1/offer',
  applyForLaunchpad: 'api/v1/launchpad/apply',
  verifyLaunchpadUrl: 'api/v1/launchpad/verify',
  cancelEthAuction: 'api/v1/listing/cancel',
  getAssetOwnerByUserId: 'api/v1/asset/owners-profile',
  createEthFixedPriceSale: 'api/v1/listing/sale',
  buyEthFixedPriceSale: 'api/v1/bid/sale',
  hideUserAssets: 'api/v1/user/asset/hide',
  getPublisherLaunchpads: 'api/v1/launchpad/by-filter',
  getAssetDetailsByTokenAddress: 'api/v1/asset/get',
  createOfferEth: 'api/v1/offer',
  acceptOfferEth: 'api/v1/offer/accept',
  getLaunchpads: 'api/v1/launchpad/by-filter',
  cancelEthOffer: 'api/v1/offer/cancel',
  getContactUs: 'api/v1/contact_us',
  getFaq: 'api/v1/faqs',
  getTAndC: 'api/v1/terms-and-conditions',
  postLike: 'api/v1/user/asset/like',
  removeLike: 'api/v1/user/asset/like',
  createDutchAuctionETH: 'api/v1/listing/dutch',
  bidDutchAuctionETH: 'api/v1/bid/dutch',
  createBulkListSaleEth: 'api/v1/listing/bulk',
  bulkBuyCartItemsEth: 'api/v1/bid/cart',
  simpleHashNftTradeHistory: 'api/v0/nfts/transfers',
  xstrelaNftTradeHistory: 'api/v1/assets/trade',
  simpleHashAssetCreators: 'api/v0/nfts',
  discordConnect: 'api/v1/users/me/connect-discord',
  instagramConnect: 'api/v1/publisher/me/connect-instagram',
  discordPublisherConnect: 'api/v1/publisher/me/connect-discord',
  instagramPublisherConnect: 'api/v1/publisher/me/connect-instagram',
  twitterPublisherConnect: 'api/v1/publisher/me/connect-twitter',
  userFollowing: 'api/v1/users/get-followings',
  userActivity: 'api/v1/user/activity',
  getOtherAsset: 'api/v1/player/asset',
  createBundleListingETH: 'api/v1/listing/bundle',
  searchGames: 'api/v1/search-games',
  gamePublisherLink: 'api/v1/publisher',
  followPublisher: 'api/v1/me/follow-publisher',
  unFollowPublisher: 'api/v1/me/unfollow-publisher',
  checkFollowPublisher: 'api/v1/me/check-follow-publisher',
  postFeedBackUrl: 'api/v1/feedback',
  updateReferralCode: 'api/v1/users/me/update-referral',
  getReferralCode: 'api/v1/users/me/referral-code',
  analyticsTrackingEvent: 'api/v1/track/event',
  postReport: 'api/v1/issue-report',
  gameOverView: 'api/v1/game-overview',
  getUserQuests: 'api/v1/user/quests/status/me',
  getQuests: 'api/v1/users/quests',
  getQuestDetails: 'api/v1/users/quests/details',
  getQuestMissions: 'api/v1/users/quest/missions',
  getFavoriteGames: 'api/v1/users/favorite-game',
  getSavedGames: 'api/v1/users/saved-game',
  getSavedContent: 'api/v1/users/saved-content',
  getFavoriteContent: 'api/v1/users/favorite-content',
  followGame: 'api/v1/users/game/follow',
  checkLikeFollowGame: 'api/v1/users/game/status',
  favoriteSavedGame: 'api/v1/users/favorite-saved-game',
  getRecentHistory: 'api/v1/users/user-recently-played/',
  getSavedEvents: 'api/v1/users/saved-event/',
  getSavedOffers: 'api/v1/users/saved-offer/',
  getMyFriends: 'api/v1/me/friends',
  getFriendRequestList: 'api/v1/me/friends/requests/pending/received',
  acceptFriendRequest: 'api/v1/me/friends/requests/{request_id}/accept',
  rejectFriendRequest: 'api/v1/me/friends/requests/{request_id}/reject',
  getPlayerProfileSectionGames: 'api/v1/users/games',
  getPlayerProfileStats: 'api/v1/users/profile-details/stats/{userid}',
  getScreenSaverSetting: 'api/v1/settings',
  getScreenSaverAnimationSetting: 'api/v1/transition-config',
  checkFriendStatus: 'api/v1/me/friends/status/{other_user_id}',
  sendFriendRequest: 'api/v1/me/friends/requests',
  unfriend: 'api/v1/me/friends/{friend_user_id}',
  getPubGames: 'api/v1/users/publisher-games',
  getPubGameGenre: 'api/v1/users/pub-game-genres',
  getPubLatestUpdates: 'api/v1/users/media-section/{id}',
  getGameFollowers: 'api/v1/users/game/follow',
  getPubFollowers: 'api/v1/me/get-followings',
  followPub: 'api/v1/me/follow-user',
  unFollowPub: 'api/v1/me/unfollow-user',
  checkPubFollow: 'api/v1/me/check-follow',
  getPubStats: 'api/v1/pub/game/me/stats',
  getPubHero: 'api/v1/users/hero-section/{id}',
  getGameBanner: 'api/v1/users/game-banner/{id}',
  getPubFollowerCount: 'api/v1/users/publisher-followers',
  getPubEvents: 'api/v1/users/pub-events',
  getPubOffers: 'api/v1/users/pub-offers',
  getScreenSavers: 'api/v1/screen-savers',
  trackIdleScreen: 'api/v1/users/idle-screen',
  getGameMedia: 'api/v1/games/media',
  games: 'api/v1/games',
  saveFavoriteImage: 'api/v1/users/favorite-saved-content/',
  getVideoPlayerVideo: 'api/v1/users/video-player/game-media',
  getVideoScene: 'api/v1/users/video-player/preview-and-thumbnails/{media_id}',
  getTrendingVideos: 'api/v1/users/video-player/trending',
  getVideosByGenre: 'api/v1/users/video-player/videos-by-genres',
  getGenreByVideo: 'api/v1/users/video-player/game-genres',
  getRelatedVideos: 'api/v1/users/video-player/related-games',
};

export default apiEndpoints;
