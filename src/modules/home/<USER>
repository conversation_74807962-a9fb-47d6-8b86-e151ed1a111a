import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import {
  getFavoriteContent,
  getFavoriteGames,
  getFavoriteVideos,
  getSavedGames,
} from 'api/game-service/game-service.api';
import { PAGE_LIMIT } from 'config/app.config';
import { BannerType } from 'enums/banner-type.enums';
import { ContentType } from 'types/focused-card.types';
import { GAME_CONTENT } from 'enums/game-content.enums';
import React, { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { gameDetailsRoute } from 'routes/path';
import StandardBanner from 'shared/banner2/standard/StandardBanner';
import { PrimaryButton } from 'shared/buttons';
import CarouselRow from 'shared/carouselRow/CarouselRow';
import { CategoryCard } from 'shared/category-card/CategoryCard';
import { FixedHeroWrapper } from 'shared/fixedHeroWrapper';
import { GameTile } from 'shared/game-tile/GameTile';
import { VideoTile } from 'shared/video-tile/VideoTile';

const genreName = ['Action', 'Adventure', 'FPS', 'Cooperative', 'Racing'];

// Dummy data for StandardBanner component
const standardBannerData = [
  {
    id: '1',
    title: 'Cyberpunk 2077',
    media_url: 'https://gaming-cdn.com/img/products/6215/pcover/1400x500/6215.jpg?v=1750336184',
    media_type: ContentType.GAME,
    game_slug: 'cyberpunk-2077',
    game_id: '1',
    trailer_banner: [],
    offer_banner: [],
    event_banner: [],
    modified_by: 'admin',
    created_at: '2024-01-01',
    display_order: 1,
    gameLogoUrl: 'https://logos-world.net/wp-content/uploads/2023/05/Cyberpunk-2077-Logo.png',
    gameTitle: 'Cyberpunk 2077',
    contentData: {
      gameLogoUrl: 'https://logos-world.net/wp-content/uploads/2023/05/Cyberpunk-2077-Logo.png',
      gameTitle: 'Cyberpunk 2077',
      stats: {
        playCount: 210000,
        followers: 850000,
        likes: 1200000,
        rating: 'M for Mature',
      },
      genres: ['RPG', 'Open World', 'Sci-Fi', 'Action'],
      onPlayClick: () => console.log('Play Cyberpunk 2077'),
      onMoreInfoClick: () => console.log('More info Cyberpunk 2077'),
    },
  },
  {
    id: '2',
    title: 'Spider-Man: Miles Morales',
    media_url:
      'https://playtracker.net/cdn-cgi/image/format=auto/https://playtracker-apollo.fra1.cdn.digitaloceanspaces.com/covers/api/game_cover_64086.jpg',
    media_type: ContentType.GAME,
    game_slug: 'spider-man-miles-morales',
    game_id: '4',
    trailer_banner: [],
    offer_banner: [],
    event_banner: [],
    modified_by: 'admin',
    created_at: '2024-01-04',
    display_order: 4,
    gameLogoUrl:
      'https://logos-world.net/wp-content/uploads/2020/11/Spider-Man-Miles-Morales-Logo.png',
    gameTitle: 'Spider-Man: Miles Morales',
    contentData: {
      gameLogoUrl:
        'https://logos-world.net/wp-content/uploads/2020/11/Spider-Man-Miles-Morales-Logo.png',
      gameTitle: 'Spider-Man: Miles Morales',
      stats: {
        playCount: 12000,
        followers: 85000,
        likes: 12043,
        rating: 'T for Teen',
      },
      genres: ['Action', 'Adventure', 'Superhero'],
      onPlayClick: () => console.log('Play Spider-Man: Miles Morales'),
      onMoreInfoClick: () => console.log('More info Spider-Man: Miles Morales'),
    },
  },
  {
    id: '3',
    title: 'Horizon Forbidden West',
    media_url:
      'https://static0.gamerantimages.com/wordpress/wp-content/uploads/2022/01/hzd.jpg?q=50&fit=crop&w=1100&h=618&dpr=1.5',
    media_type: ContentType.GAME,
    game_slug: 'horizon-forbidden-west',
    game_id: '5',
    trailer_banner: [],
    offer_banner: [],
    event_banner: [],
    modified_by: 'admin',
    created_at: '2024-01-05',
    display_order: 5,
    gameLogoUrl:
      'https://logos-world.net/wp-content/uploads/2022/02/Horizon-Forbidden-West-Logo.png',
    gameTitle: 'Horizon Forbidden West',
    contentData: {
      gameLogoUrl:
        'https://logos-world.net/wp-content/uploads/2022/02/Horizon-Forbidden-West-Logo.png',
      gameTitle: 'Horizon Forbidden West',
      stats: {
        playCount: 3200,
        followers: 80,
        likes: 12000,
        rating: 'T for Teen',
      },
      genres: ['Action', 'RPG', 'Open World'],
      onPlayClick: () => console.log('Play Horizon Forbidden West'),
      onMoreInfoClick: () => console.log('More info Horizon Forbidden West'),
    },
  },
];

const Home = () => {
  const { ref, focusKey, focusSelf } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: 'other-user-profile',
  });

  // Focus the page when it mounts and set focus to first item
  React.useEffect(() => {
    const timer = setTimeout(() => {
      focusSelf();
    }, 100); // Longer delay to wait for RecommendedRow data to load (800ms API + buffer)

    return () => clearTimeout(timer);
  }, [focusSelf]);

  const navigate = useNavigate();

  const onAssetPress = useCallback(
    (props: object) => {
      // Handle asset press - navigate to details page with game slug
      const propsWithGameSlug = props as Record<string, unknown>;
      if (propsWithGameSlug?.gameSlug) {
        navigate(gameDetailsRoute + '/' + (propsWithGameSlug.gameSlug as string));
      }
    },
    [navigate]
  );

  const onRowFocus = useCallback(
    ({ y }: { y: number }) => {
      (ref.current as HTMLDivElement)?.scrollTo({
        top: y + 400,
        behavior: 'smooth',
      });
    },
    [ref]
  );

  return (
    <FocusContext.Provider value={focusKey}>
      <FixedHeroWrapper
        ref={ref}
        hero={
          <StandardBanner
            data={standardBannerData}
            autoPlay={true}
            autoPlayInterval={5000}
            showNavigation={true}
            bannerType={BannerType.DYNAMIC}
          />
        }
      >
        <PrimaryButton
          onClick={() => navigate('/profile/d87d452e-aa11-42f1-9e3c-c1bca6b4641d')}
          focusKey="other-user-profile"
        >
          Other user Profile
        </PrimaryButton>
        <PrimaryButton
          // onClick={() => navigate('/publisher/19d57051-eaa4-479e-bf5d-6912bcb76a35')} //qa
          onClick={() => navigate('/publisher/70798cad-52d9-4b79-9219-a958eaec0ec9')} //dev
          focusKey="other-user-profile"
        >
          Publisher Details
        </PrimaryButton>
        <CarouselRow
          title="Continue Playing"
          handleApiCall={async page => {
            return await getFavoriteGames(page, PAGE_LIMIT);
          }}
          renderItem={(item, index) => (
            <GameTile
              gameSlug={item?.game_slug}
              videoUrl={
                item?.default_banner_media_type === 'VIDEO' ? item?.default_banner_media_url : ''
              }
              index={index}
              name={item.title ?? ''}
              imageUrl={item.image_url}
              onEnterPress={onAssetPress}
              esrbRating={item?.esrb}
              onCardFocused={props => {
                console.log('Favorited Games GameTile onCardFocused - Props:', props, item);
              }}
            />
          )}
          onFocus={onRowFocus}
        />
        <CarouselRow
          title="Recently Added"
          handleApiCall={async page => {
            return await getSavedGames(page, PAGE_LIMIT);
          }}
          renderItem={(item, index) => (
            <GameTile
              gameSlug={item?.game_slug}
              videoUrl={
                item?.default_banner_media_type === 'VIDEO' ? item?.default_banner_media_url : ''
              }
              index={index}
              name={item.title ?? ''}
              imageUrl={item.image_url}
              onEnterPress={onAssetPress}
              esrbRating={item?.esrb}
              onFocus={(layout, props) => {
                console.log('Saved Games GameTile focused - Props:', props);
              }}
              onCardFocused={props => {
                console.log('Saved Games GameTile onCardFocused - Props:', props, item);
              }}
            />
          )}
          onFocus={onRowFocus}
        />

        <CarouselRow
          title="Trending Videos"
          handleApiCall={async page => {
            return await getFavoriteVideos(page, PAGE_LIMIT);
          }}
          renderItem={item => (
            <VideoTile
              isVerified={item?.is_verified}
              videoUrl={item?.url}
              channelLogo={item?.company_image}
              duration={item?.duration}
              thumbnailUrl={item?.thumbnail_url}
              channelName={item?.company_name}
              key={item?.media_id}
              timeAgo={item?.created_at}
              title={item?.title}
              onEnterPress={onAssetPress}
              onFocus={() => {}}
              onCardFocused={props => {
                console.log('Favorited Videos VideoTile onCardFocused - Props:', props, item);
              }}
            />
          )}
          onFocus={onRowFocus}
        />
        <CarouselRow
          title="Genres"
          handleApiCall={async page => {
            return await getFavoriteContent(GAME_CONTENT.IMAGE, page, PAGE_LIMIT);
          }}
          renderItem={(item, i) => (
            <CategoryCard
              imageUrl={item?.thumbnail_url ?? item?.url}
              categoryName={genreName[i]}
              key={item?.media_id}
              onFocus={() => {}}
            />
          )}
          onFocus={onRowFocus}
          skeletonVariant="CATEGORY_CARD"
        />
      </FixedHeroWrapper>
    </FocusContext.Provider>
  );
};

export default Home;
