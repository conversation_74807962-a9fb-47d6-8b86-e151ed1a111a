import { IconGroup } from 'shared/IconGroup';

import {
  checkLikeFollowGameStatus,
  favoriteSaveGame,
  followGame,
  unFollowGame,
} from 'api/game-service/game-service.api';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { AiFillMinusCircle } from 'react-icons/ai';
import { CiBookmark, CiHeart } from 'react-icons/ci';
import { FaCloud, FaDiscord, FaHeart, FaKeyboard, FaMobileAlt, FaWindows } from 'react-icons/fa';
import { IoMdAddCircle } from 'react-icons/io';
import { IoBookmark, IoGameController } from 'react-icons/io5';
import { IconButton, PrimaryButton } from 'shared/buttons';
import { Col, Row } from 'styles/sharedStyles';
import { IGame } from 'types/api/game/details.game.api.types';
import { formatUnixTimestamp } from 'utils/helpers.utils';
import {
  DescriptionHeading,
  DescriptionText,
  InfoContainer,
  MetadataItem,
  MetadataLabel,
  MetadataList,
  MetadataValue,
  ScoreBadge,
  ScreenWrapper,
  Tag,
  TagGroup,
} from './style';
import {
  FocusContext,
  useFocusable,
  type FocusableComponentLayout,
} from '@noriginmedia/norigin-spatial-navigation';

// --- Main Component ---
export function GameOverview({
  gameData,
  id,
  onButtonFocus,
}: {
  gameData?: IGame;
  id?: string;
  onButtonFocus?: (layout?: FocusableComponentLayout) => void;
}) {
  const { ref, focusKey } = useFocusable({
    focusable: true,
    preferredChildFocusKey: 'play-game-key',
    trackChildren: true,
  });

  const onPlay = () => alert('Playing game...');
  const [isFavorite, setIsFavorite] = useState(false);
  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false);
  const [isSave, setIsSave] = useState(false);
  const [isSaveLoading, setIsSaveLoading] = useState(false);

  const [isFollow, setIsFollow] = useState(false);
  const [isFollowLoading, setIsFollowLoading] = useState(false);

  const fetchUserGameStatus = useCallback(async () => {
    if (id) {
      checkLikeFollowGameStatus(id).then(res => {
        setIsFavorite(res.is_favourite);
        setIsSave(res?.is_saved);
        setIsFollow(res.is_follow);
      });
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchUserGameStatus();
    }
  }, [id, fetchUserGameStatus]);

  const handleFollow = async () => {
    if (!id || isFollowLoading) return;
    try {
      setIsFollowLoading(true);
      if (isFollow) {
        await unFollowGame(id);
      } else {
        await followGame(id);
      }

      toast.success(
        `${gameData?.game_title ?? 'Game'} ${isFollow ? 'unfollowed' : 'followed'} successfully`
      );

      await fetchUserGameStatus();
    } catch (error) {
      console.error(error);
    } finally {
      setIsFollowLoading(false);
    }
  };

  const handleFavorite = async () => {
    if (!id || isFavoriteLoading) return;
    try {
      setIsFavoriteLoading(true);
      await favoriteSaveGame(id, !isFavorite);

      toast.success(
        `${gameData?.game_title ?? 'Game'} is  ${!isFavorite ? 'added to' : 'removed from'} favorite successfully`
      );

      await fetchUserGameStatus();
    } catch (error) {
      console.error(error);
    } finally {
      setIsFavoriteLoading(false);
    }
  };
  const handleSave = async () => {
    if (!id || isSaveLoading) return;
    try {
      setIsSaveLoading(true);
      await favoriteSaveGame(id, undefined, !isSave);

      toast.success(
        `${gameData?.game_title ?? 'Game'} is  ${!isSave ? 'added to' : 'removed from'} save successfully`
      );

      await fetchUserGameStatus();
    } catch (error) {
      console.error(error);
    } finally {
      setIsSaveLoading(false);
    }
  };

  return (
    <FocusContext.Provider value={focusKey}>
      <ScreenWrapper ref={ref}>
        <Row gap="158px" justify="space-between">
          {/* LEFT COLUMN */}
          <Col width="100%" gap="74px">
            <Row gap="32px">
              <PrimaryButton
                width="275px"
                icon={<IoGameController size="48px" />}
                focusKey="play-game-key"
                onClick={onPlay}
                onFocus={onButtonFocus}
              >
                Play
              </PrimaryButton>
              <PrimaryButton
                width="275px"
                focusKey="follow-key"
                onClick={handleFollow}
                isLoading={isFollowLoading}
                icon={
                  isFollow ? <AiFillMinusCircle fontSize={48} /> : <IoMdAddCircle fontSize={48} />
                }
                onFocus={onButtonFocus}
              >
                {isFollow ? 'Unfollow' : 'Follow'}
              </PrimaryButton>
              <Row gap="40px">
                <IconButton
                  width="100px"
                  focusKey="bookmark-key"
                  onClick={handleSave}
                  isLoading={isSaveLoading}
                  icon={isSave ? <IoBookmark fontSize={48} /> : <CiBookmark fontSize={48} />}
                  onFocus={onButtonFocus}
                />
                <IconButton
                  width="100px"
                  focusKey="favorite-key"
                  onClick={handleFavorite}
                  isLoading={isFavoriteLoading}
                  icon={isFavorite ? <FaHeart fontSize={48} /> : <CiHeart fontSize={48} />}
                  onFocus={onButtonFocus}
                />
              </Row>
            </Row>
            <Col gap="48px">
              <Col gap="16px">
                <DescriptionHeading>Game Description:</DescriptionHeading>
                <DescriptionText>{gameData?.description ?? gameData?.short_bio}</DescriptionText>
              </Col>

              <TagGroup>
                {gameData?.tags?.slice(0, 5)?.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </TagGroup>
            </Col>
          </Col>

          {/* RIGHT COLUMN */}
          <InfoContainer width="100%">
            <MetadataList>
              <MetadataItem>
                <MetadataLabel>Metacritic Score</MetadataLabel>
                <ScoreBadge>{gameData?.star_rating?.toFixed(1)}</ScoreBadge>
              </MetadataItem>
              <MetadataItem>
                <MetadataLabel>Release Date</MetadataLabel>
                <MetadataValue>{formatUnixTimestamp(gameData?.release_date ?? 0)}</MetadataValue>
              </MetadataItem>
              <MetadataItem>
                <MetadataLabel>Developer</MetadataLabel>
                <MetadataValue>{gameData?.developers?.join(', ')}</MetadataValue>
              </MetadataItem>
              <MetadataItem>
                <MetadataLabel>Publisher</MetadataLabel>
                <MetadataValue>{gameData?.publisher_display_name}</MetadataValue>
              </MetadataItem>
              <MetadataItem>
                <MetadataLabel>Platforms</MetadataLabel>
                <IconGroup
                  icons={[
                    <FaWindows key="win" />,
                    <FaMobileAlt key="mob" />,
                    <FaCloud key="cld" />,
                  ]}
                />
              </MetadataItem>
              <MetadataItem>
                <MetadataLabel>Controller Options</MetadataLabel>
                <IconGroup
                  icons={[
                    <FaDiscord key="disc" />,
                    <IoGameController key="ctrl" />,
                    <FaKeyboard key="keyb" />,
                  ]}
                />
              </MetadataItem>
            </MetadataList>
          </InfoContainer>
        </Row>
      </ScreenWrapper>
    </FocusContext.Provider>
  );
}
