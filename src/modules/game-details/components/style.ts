import styled from 'styled-components';
import { Col } from 'styles/sharedStyles';
import { BodyMD, BodyXL, HeadingLG, HeadingMD } from 'styles/theme/typography';

export const ScreenWrapper = styled.div`
  padding: 24px 0 48px;
  background-color: ${({ theme }) => theme.bgColor};
  color: #e0e0e0;
  font-family: sans-serif;
`;

export const ActionsWrapper = styled.div``;

export const DescriptionHeading = styled(HeadingLG)`
  color: ${({ theme }) => theme.iconWhite};
`;

export const DescriptionText = styled(BodyXL)`
  color: ${({ theme }) => theme.textPrimary};
`;

export const TagGroup = styled.div`
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  align-items: center;
`;
export const Tag = styled(BodyMD)`
  display: flex;
  align-items: center;
  gap: 24px;

  /* Apply the ::after pseudo-element to every Tag that is NOT the last child */
  &:not(:last-child)::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    background-color: ${({ theme }) => theme.offWhite};
    border-radius: 50%;
  }
`;

export const MetadataList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`;

export const MetadataItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 24px;
  gap: 140px;
  border-bottom: 1px solid ${({ theme }) => theme.perlWhite};
`;

export const MetadataLabel = styled(HeadingMD)``;

export const MetadataValue = styled(BodyXL)`
  word-break: break-all;
  -webkit-hyphens: auto; /* For Safari and older Chrome */
  -moz-hyphens: auto; /* For Firefox */
  hyphens: auto;
`;

export const ScoreBadge = styled(HeadingMD)`
  background-color: ${({ theme }) => theme.bgColorOpacity90};
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid ${({ theme }) => theme.textPrimary};
`;
export const InfoContainer = styled(Col)`
  background-color: ${({ theme }) => theme.surfaceSecondaryOpacity100};
  padding: 32px;
  border-radius: 8px;
`;
