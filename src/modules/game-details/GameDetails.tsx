import {
  FocusableComponentLayout,
  FocusContext,
  useFocusable,
} from '@noriginmedia/norigin-spatial-navigation';
import {
  getGameBanner,
  getGameDetails,
  getGameMedia,
  getPubGames,
} from 'api/game-service/game-service.api';
import { PAGE_LIMIT } from 'config/app.config';
import { GAME_MEDIA } from 'enums/game-media.enums';

import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import StandardBanner from 'shared/banner2/standard/StandardBanner';
import CarouselRow from 'shared/carouselRow/CarouselRow';
import { GameTile } from 'shared/game-tile/GameTile';
import { VideoTile } from 'shared/video-tile/VideoTile';
import { IGame } from 'types/api/game/details.game.api.types';
import { GameBannerItem } from 'types/banner.types';
import { GameOverview } from './components/GameOverview';
import StickyHeroWrapper from 'shared/fixedHeroWrapper/StickyHeroWrapper';
import LibraryOmbre from 'shared/banners/ombre/LibraryOmbre';
import { ContentType } from 'types/focused-card.types';
import { BannerType } from 'enums/banner-type.enums';
import styled from 'styled-components';
import { useRemoteControl } from 'hooks/useRemoteControl';
import { CircularLoader, Row } from 'styles/sharedStyles';

interface FocusedCardData {
  contentType: ContentType;
  gameLogoUrl?: string;
  name?: string;
  title?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  bannerUrl?: string;
  playCount?: number;
  followerCount?: string | number;
  favoriteCount?: string | number;
  esrbRating?: string;
  esrbRatingName?: string;
  genres?: string[];
  uploadDate?: string;
  duration?: number;
  channelLogo?: string;
  channelName?: string;
  isVerified?: boolean;
  timeAgo?: string;
}

// Import types from StandardBanner
interface BannerItemData {
  id: string;
  media_url: string;
  start_date: string;
  end_date: string | null;
  title: string;
  is_saved_event?: boolean;
  is_saved_offer?: boolean;
}

interface GameData {
  gameLogoUrl?: string;
  gameTitle?: string;
  stats?: {
    playCount?: number;
    followers?: number;
    likes?: number;
    rating?: string;
    saveCount?: number;
  };
  genres?: string[];
  onPlayClick?: () => void;
  onMoreInfoClick?: () => void;
}

const GameDetails = () => {
  const { id = 'black-myth-wukong' } = useParams<{ id: string }>();
  const { ref, focusKey, focusSelf } = useFocusable({
    focusable: true,
    saveLastFocusedChild: true,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
    focusKey: 'homepage',
    preferredChildFocusKey: 'nav-preview-0', // Focus first image indicator
  });

  const [gameDetails, setGameDetails] = useState<IGame>();
  const [gameBannerData, setGameBannerData] = useState<GameBannerItem[]>([]);
  const [focusedCardData, setFocusedCardData] = useState<FocusedCardData | null>(null);
  const [isGameOverviewButtonsFocused, setIsGameOverviewButtonsFocused] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Focus will be set after loading is complete in GetGameDetails

  const GetGameDetails = async () => {
    if (!id) return;
    try {
      setIsLoading(true);
      // Reset focused card data when loading starts
      setFocusedCardData(null);
      const res = await getGameDetails(id);
      setGameDetails(res);
    } catch (error) {
      console.error('Failed to fetch game details:', error);
    } finally {
      setIsLoading(false);
      // Set focus after loading is complete
      setTimeout(() => {
        focusSelf();
      }, 100);
    }
  };

  const GetGameBanner = async () => {
    if (!id) return;
    try {
      const res = await getGameBanner(id);
      // Sort by display_order to ensure proper banner sequence
      const sortedData = (res.data || []).sort(
        (a: unknown, b: unknown) =>
          (a as GameBannerItem).display_order - (b as GameBannerItem).display_order
      );
      setGameBannerData(sortedData as GameBannerItem[]);
    } catch (error) {
      console.error('Failed to fetch game banner:', error);
      setGameBannerData([]);
    }
  };

  useEffect(() => {
    if (id) {
      GetGameDetails();
      GetGameBanner();
    }
  }, [id]); // eslint-disable-line

  const navigate = useNavigate();

  const onAssetPress = useCallback(
    (asset: { index?: number; title?: string; gameSlug?: string; slug?: string }) => {
      // Handle asset press - navigate to details page with actual game slug/ID
      const assetId =
        asset.gameSlug || asset.slug || asset.index || Math.random().toString(36).substr(2, 9);
      navigate(`/game/${assetId}`);
    },
    [navigate]
  );
  // Create a function to handle row focus with customizable offsets
  const onRowFocus = useCallback(
    (layout?: FocusableComponentLayout, extraValue: number = 0) => {
      if (layout) {
        (ref.current as HTMLDivElement)?.scrollTo({
          top: layout.y + extraValue,
          behavior: 'smooth',
        });
      }
    },
    [ref]
  );

  // Handle banner indicator focus
  const handleBannerIndicatorFocus = useCallback(() => {
    // Clear focused card data when banner indicators are focused
    setFocusedCardData(null);
    // Optional: Handle any specific banner indicator focus logic here
  }, [setFocusedCardData]);

  const getCurrentBannerData = useCallback(() => {
    // If we have focused card data, create data for LibraryOmbre
    if (focusedCardData) {
      switch (focusedCardData.contentType) {
        case ContentType.GAME:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            gameTitle: focusedCardData?.name || focusedCardData?.title || 'Unknown Game',
            bannerUrl:
              focusedCardData?.bannerUrl ||
              focusedCardData?.imageUrl ||
              focusedCardData?.thumbnailUrl,
            contentType: ContentType.GAME,
            stats: {
              players: focusedCardData?.playCount ?? 0,
              followers: focusedCardData?.followerCount ?? 0,
              likes: focusedCardData?.favoriteCount ?? 0,
              rating:
                (focusedCardData?.esrbRatingName as string) ||
                (focusedCardData?.esrbRating as string) ||
                'Not Rated',
            },
            genres: (focusedCardData?.genres as string[]) || [],
            onPlayClick: () =>
              console.log(
                'Play focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
            onMoreInfoClick: () =>
              console.log(
                'More info for focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
          };
        case ContentType.VIDEO:
          return {
            gameTitle:
              (focusedCardData?.title as string) ||
              (focusedCardData?.name as string) ||
              'Unknown Video',
            bannerUrl:
              (focusedCardData?.thumbnailUrl as string) || (focusedCardData?.imageUrl as string),
            contentType: ContentType.VIDEO,
            uploadDate: focusedCardData?.uploadDate as string,
            duration: focusedCardData?.duration as number,
            publisherInfo: {
              imageUrl: (focusedCardData?.channelLogo as string) || '',
              publisherName: (focusedCardData?.channelName as string) || 'Unknown Publisher',
              isVerified: focusedCardData?.isVerified || false,
            },
            stats: {
              uploadTime:
                (focusedCardData?.uploadDate as string) ||
                (focusedCardData?.timeAgo as string) ||
                'Recently',
              views: focusedCardData?.playCount ?? 0,
              likes: focusedCardData?.favoriteCount ?? 0,
            },
            onPlayClick: () =>
              console.log('Play focused video clicked:', focusedCardData?.title as string),
            onSaveClick: () =>
              console.log('Save focused video clicked:', focusedCardData?.title as string),
          };
        default:
          // Return a default game data structure for unsupported types
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl,
            gameTitle: 'Unknown Content',
            bannerUrl: focusedCardData?.imageUrl || focusedCardData?.thumbnailUrl,
            contentType: ContentType.GAME,
            stats: {
              players: 0,
              followers: 0,
              likes: 0,
              rating: 'Not Rated',
            },
            genres: [],
            onPlayClick: () => console.log('Play clicked'),
            onMoreInfoClick: () => console.log('More info clicked'),
          };
      }
    }
    return null;
  }, [focusedCardData]);

  // Convert GameBannerItem[] to BannerData[] format
  const convertedBannerData = gameBannerData.map(item => ({
    id: item.id,
    title: item.title || 'Game Title',
    media_url: item.media_url ?? '',
    media_type: item.media_type || 'IMAGE',
    game_slug: item.game_slug || id || '',
    game_id: item.game_id || '',
    trailer_banner: (item.trailer_banner || []) as BannerItemData[],
    offer_banner: (item.offer_banner || []) as BannerItemData[],
    event_banner: (item.event_banner || []) as BannerItemData[],
    modified_by: item.modified_by || '',
    created_at: item.created_at || new Date().toISOString(),
    display_order: item.display_order || 0,
    // Additional properties for component data
    gameLogoUrl: gameDetails?.game_text_img_url,
    gameTitle: item.title || 'Game Title',
    contentData: {
      gameLogoUrl: gameDetails?.game_text_img_url,
      gameTitle: item.title || 'Game Title',
      stats: {
        playCount: item?.play_count || 0,
        followers: item?.follower_count || 0,
        likes: item?.favorite_count || 0,
        saveCount: item?.saved_count || 0,
        rating: 'E for Everyone',
      },
      genres: item?.genres,
    } as GameData,
    // For backward compatibility
    imageUrl: item.media_url ?? '',
  }));

  useRemoteControl({
    onArrowUp: () => {
      if (isGameOverviewButtonsFocused) setIsGameOverviewButtonsFocused(false);
    },
  });

  if (isLoading) {
    return (
      <Row width="100%" height="100vh" align="center" justify="center">
        <CircularLoader size={75} />
      </Row>
    );
  }

  return (
    <FocusContext.Provider value={focusKey}>
      <StickyHeroWrapper
        customTransformValue="0"
        heroSection={
          isGameOverviewButtonsFocused ? (
            // Don't show any banner when GameOverview buttons are focused
            <div style={{ height: '100px', background: 'transparent' }} />
          ) : focusedCardData && getCurrentBannerData() ? (
            <LibraryOmbre data={getCurrentBannerData()!} bannerType={BannerType.DYNAMIC} />
          ) : (
            <StandardBanner
              data={convertedBannerData.length > 0 ? convertedBannerData : undefined}
              onIndicatorFocus={handleBannerIndicatorFocus}
            />
          )
        }
        scrollableSection={
          <ContentContainer ref={ref}>
            <GameOverview
              id={id}
              gameData={gameDetails}
              onButtonFocus={layout => {
                // Set state to indicate GameOverview buttons are focused
                setIsGameOverviewButtonsFocused(true);
                // Clear focused card data when GameOverview buttons are focused
                setFocusedCardData(null);
                // Call the original focus handler with layout
                if (layout) {
                  onRowFocus(layout);
                }
              }}
            />

            <CarouselRow
              title="Trailers and Screenshots"
              handleApiCall={async page => {
                return await getGameMedia(id ?? '', page, PAGE_LIMIT, [
                  GAME_MEDIA.TRAILER,
                  GAME_MEDIA.IMAGE,
                ]);
              }}
              renderItem={(item, index) =>
                item?.media_type == GAME_MEDIA?.TRAILER ? (
                  <VideoTile
                    isVerified={false}
                    videoUrl={item?.url}
                    channelLogo={item?.studio_url}
                    duration={item?.duration ?? 0}
                    thumbnailUrl={item?.thumbnail_url ?? item?.url ?? ''}
                    channelName={item?.studio_name}
                    key={item?.id}
                    timeAgo={item?.created_at ?? ''}
                    title={item?.title ?? ''}
                    onEnterPress={onAssetPress}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      setIsGameOverviewButtonsFocused(false);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.VIDEO,
                        uploadDate: item?.created_at || undefined,
                        channelName: item?.studio_name || undefined,
                        channelLogo: item?.studio_url || undefined,
                        isVerified: false,
                        duration: item?.duration ?? undefined,
                        thumbnailUrl: item?.thumbnail_url ?? item?.url,
                        followerCount: item?.favorite_count,
                        playCount: item?.view_count,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                ) : (
                  <GameTile
                    gameSlug={item?.id ?? ''}
                    index={index}
                    name={item.title ?? ''}
                    imageUrl={item.thumbnail_url ?? item?.url}
                    onEnterPress={onAssetPress}
                    esrbRating={''}
                    onCardFocused={props => {
                      setIsGameOverviewButtonsFocused(false);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.GAME,
                        imageUrl: item.thumbnail_url ?? item?.url,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )
              }
              onFocus={layout => onRowFocus(layout, -10)}
            />

            <CarouselRow
              title="Videos from this Game"
              handleApiCall={async page => {
                return await getGameMedia(id ?? '', page, PAGE_LIMIT, [GAME_MEDIA.VIDEO]);
              }}
              renderItem={item => (
                <VideoTile
                  isVerified={false}
                  videoUrl={item?.url}
                  channelLogo={item?.studio_url}
                  duration={item?.duration ?? 0}
                  thumbnailUrl={item?.thumbnail_url ?? item?.url ?? ''}
                  channelName={item?.studio_name}
                  key={item?.id}
                  timeAgo={item?.created_at ?? ''}
                  title={item?.title ?? ''}
                  onEnterPress={onAssetPress}
                  onFocus={() => {}}
                  onCardFocused={props => {
                    setIsGameOverviewButtonsFocused(false);
                    const focusedData: FocusedCardData = {
                      ...(props as object),
                      contentType: ContentType.VIDEO,
                      uploadDate: item?.created_at || undefined,
                      channelName: item?.studio_name || undefined,
                      channelLogo: item?.studio_url || undefined,
                      isVerified: false,
                      duration: item?.duration ?? undefined,
                      thumbnailUrl: item?.thumbnail_url ?? item?.url,
                      followerCount: item?.favorite_count,
                      playCount: item?.view_count,
                    };
                    setFocusedCardData(focusedData);
                  }}
                />
              )}
              onFocus={layout => onRowFocus(layout, -10)}
            />
            {gameDetails?.publisher_id ? (
              <CarouselRow
                title="More from this Publisher"
                handleApiCall={async page => {
                  // Because of the conditional render, gameDetails.publisher_id is guaranteed to exist here
                  return await getPubGames(gameDetails.publisher_id ?? '', page, PAGE_LIMIT);
                }}
                renderItem={(item, index) => (
                  <GameTile
                    key={item?.slug || index} // It's safer to use a unique ID from the item if available
                    gameSlug={item?.slug ?? ''}
                    index={index}
                    name={item.name ?? ''}
                    imageUrl={item.cover_url ?? ''}
                    onEnterPress={asset =>
                      onAssetPress({
                        ...asset,
                        gameSlug: item?.slug ?? undefined,
                        slug: item?.slug ?? undefined,
                      })
                    }
                    videoUrl={item?.trailer_url ?? ''}
                    esrbRating={item?.esrb_img_url ?? ''}
                    onCardFocused={props => {
                      setIsGameOverviewButtonsFocused(false);

                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.GAME,
                        imageUrl: item.cover_url ?? '',
                        name: item.name ?? '',
                        esrbRatingName: item?.esrb_description || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 600)}
              />
            ) : (
              'Loading'
            )}
          </ContentContainer>
        }
      />
    </FocusContext.Provider>
  );
};

export default GameDetails;

const ContentContainer = styled.div`
  padding: 0 48px 48px 32px;
  color: ${({ theme }) => theme.textPrimary};
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: ${({ theme }) => theme.gameTileOverlayGradient};
  padding-bottom: 100px;
`;
