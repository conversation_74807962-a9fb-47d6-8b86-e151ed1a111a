import { setFocus, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import {
  getFavoriteContent,
  getFavoriteGames,
  getFavoriteVideos,
  getRecentHistory,
  getSavedContent,
  getSavedEvents,
  getSavedGames,
  getSavedOffers,
  getSavedVideos,
} from 'api/game-service/game-service.api';
import logo from 'assets/logo/GameTitleLogo.png';
import { PAGE_LIMIT } from 'config/app.config';
import { GAME_CONTENT } from 'enums/game-content.enums';
import React, { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { gameInterstitialRoute } from 'routes/path';
import LibraryOmbre from 'shared/banners/ombre/LibraryOmbre';
import CarouselRow from 'shared/carouselRow/CarouselRow';
import StickyHeroWrapper from 'shared/fixedHeroWrapper/StickyHeroWrapper';

import { GameTile } from 'shared/game-tile/GameTile';
import { ImageCard } from 'shared/image-card/ImageCard';
import { PromoCard } from 'shared/promo-card/PromoCard';
import { VideoTile } from 'shared/video-tile/VideoTile';
import styled from 'styled-components';
import { CircularLoader, Row } from 'styles/sharedStyles';
import { ContentType, FocusedCardData } from 'types/focused-card.types';
import { formatDateTime } from 'utils/dateUtils';

const ContentContainer = styled.div`
  padding: 0 32px;
  color: ${({ theme }) => theme.textPrimary};
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: ${({ theme }) => theme.gameTileOverlayGradient};
`;

// Dummy data for different content types
const gameData = {
  gameLogoUrl: logo,
  gameTitle: 'Cyberpunk 2077',
  contentType: ContentType.GAME,
  stats: {
    players: '2.1M',
    followers: '850K',
    likes: '1.2M',
    rating: 'M for Mature',
  },
  genres: ['RPG', 'Open World', 'Sci-Fi', 'Action'],
  onPlayClick: () => console.log('Play game clicked'),
  onMoreInfoClick: () => console.log('More game info clicked'),
};

const LibraryPage = () => {
  const [focusedCardData, setFocusedCardData] = useState<FocusedCardData | null>(null);
  const [initialHeroData, setInitialHeroData] = useState<object | null>(null);
  const [isLoadingInitialData, setIsLoadingInitialData] = useState(true);
  const [firstAvailableCarousel, setFirstAvailableCarousel] = useState<string | null>(null);
  const navigate = useNavigate();

  // Refetch trigger states for events and offers
  const [refetchSavedEvents, setRefetchSavedEvents] = useState(false);
  const [refetchSavedOffers, setRefetchSavedOffers] = useState(false);

  // Define carousel order for focus priority
  const carouselOrder = [
    'Recent History',
    'Favorited Games',
    'Saved Games',
    'Favorited Videos',
    'Saved Videos',
    'Saved Events',
    'Saved Offers',
  ];

  const { ref, focusSelf } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    isFocusBoundary: false,
    onArrowPress: direction => {
      // Allow left arrow to escape to sidebar
      if (direction === 'left') {
        return false; // Don't handle, let spatial navigation find sidebar
      }
      return true; // Handle other directions normally
    },
  });

  // Fetch initial data from all carousels in parallel
  React.useEffect(() => {
    const fetchAllCarouselData = async () => {
      // Define carousel API calls in priority order
      const carouselApis = [
        {
          name: 'Recent History',
          apiCall: () => getRecentHistory(1, 1),
          contentType: ContentType.GAME,
        },
        {
          name: 'Favorited Games',
          apiCall: () => getFavoriteGames(1, 1),
          contentType: ContentType.GAME,
        },
        { name: 'Saved Games', apiCall: () => getSavedGames(1, 1), contentType: ContentType.GAME },
        {
          name: 'Favorited Videos',
          apiCall: () => getFavoriteVideos(1, 1),
          contentType: ContentType.VIDEO,
        },
        {
          name: 'Saved Videos',
          apiCall: () => getSavedVideos(1, 1),
          contentType: ContentType.VIDEO,
        },
        {
          name: 'Saved Events',
          apiCall: () => getSavedEvents(1, 1),
          contentType: ContentType.EVENT,
        },
        {
          name: 'Saved Offers',
          apiCall: () => getSavedOffers(1, 1),
          contentType: ContentType.OFFER,
        },
      ];

      try {
        // Fetch all APIs in parallel
        const results = await Promise.allSettled(
          carouselApis.map(async carousel => {
            try {
              const response = await carousel.apiCall();
              return {
                ...carousel,
                data: response?.data || [],
                hasData: response?.data && response.data.length > 0,
              };
            } catch (error) {
              console.error(`Error fetching ${carousel.name}:`, error);
              return {
                ...carousel,
                data: [],
                hasData: false,
              };
            }
          })
        );

        // Find first carousel with data according to priority order
        let firstCarouselWithData = null;
        let firstItemData = null;

        for (const result of results) {
          if (result.status === 'fulfilled' && result.value.hasData) {
            firstCarouselWithData = result.value.name;
            const firstItem = result.value.data[0];
            // Create initial hero data based on content type
            const item = firstItem as unknown as Record<string, unknown>; // Cast to avoid type conflicts

            if (result.value.contentType === ContentType.GAME) {
              firstItemData = {
                gameLogoUrl: (item?.gameLogoUrl as string) ?? '',
                gameTitle: (item?.title as string) || 'Unknown Game',
                bannerUrl:
                  (item?.default_banner_media_url as string) || (item?.image_url as string) || '',
                contentType: ContentType.GAME,
                stats: {
                  players: (item?.play_count as string) || '2.1M',
                  followers: (item?.follower_count as string) || '850K',
                  likes: (item?.favorite_count as string) || '1.2M',
                  rating:
                    (item?.esrb_description as string) || (item?.esrb as string) || 'Not Rated',
                },
                genres: (item?.genres as string[]) || [],
                onPlayClick: () => console.log('Play initial game clicked:', item?.title),
                onMoreInfoClick: () =>
                  console.log('More info for initial game clicked:', item?.title),
              };
            } else if (result.value.contentType === ContentType.VIDEO) {
              firstItemData = {
                gameTitle: item?.title || 'Unknown Video',
                bannerUrl: item?.thumbnail_url || '',
                contentType: ContentType.VIDEO,
                uploadDate: item?.created_at || '',
                publisherInfo: {
                  imageUrl: (item?.company_image as string) || '',
                  publisherName: (item?.company_name as string) || 'Unknown Publisher',
                  isVerified: (item?.is_verified as boolean) || false,
                },
                stats: {
                  uploadTime: (item?.created_at as string) || 'Recently',
                  views: (item?.play_count as string) ?? '0',
                  likes: (item?.favorite_count as string) ?? '0',
                },
                onPlayClick: () => console.log('Play initial video clicked:', item?.title),
                onSaveClick: () => console.log('Save initial video clicked:', item?.title),
              };
            } else {
              // EVENT or OFFER
              firstItemData = {
                gameLogoUrl: '',
                eventTitle: focusedCardData?.title ?? 'Unknown Event',
                bannerUrl: (item?.image_url as string) || '',
                contentType: ContentType.EVENT,
                stats: {
                  dropDate: item?.start_date
                    ? formatDateTime(item.start_date as string)
                    : '12/10/24',
                  expires: item?.end_date ? formatDateTime(item.end_date as string) : '01/15/25',
                  views: (item?.play_count as string) || '89K',
                  saves: (item?.favorite_count as string) || '12K',
                },
                onMoreInfoClick: () =>
                  console.log('More info for initial event clicked:', item?.company_name),
                onSaveClick: () => console.log('Save initial event clicked:', item?.company_name),
              };
            }
            break; // Use first available carousel according to priority
          }
        }

        if (firstCarouselWithData && firstItemData) {
          setInitialHeroData(firstItemData);
          setFirstAvailableCarousel(firstCarouselWithData);
          console.log(`Initial hero data loaded from ${firstCarouselWithData}:`, firstItemData);
        }
      } catch (error) {
        console.error('Error fetching carousel data:', error);
      } finally {
        setIsLoadingInitialData(false);
      }
    };

    fetchAllCarouselData();
  }, []); // eslint-disable-line

  // Focus after data is loaded
  React.useEffect(() => {
    if (!isLoadingInitialData) {
      const timer = setTimeout(() => {
        if (firstAvailableCarousel) {
          // Focus on first carousel's first card based on priority
          const focusKey = `${firstAvailableCarousel}-0`;
          try {
            setFocus(focusKey);
            console.log(`Focus set to: ${focusKey}`);
          } catch {
            // If that fails, focus on the page itself
            focusSelf();
          }
        } else {
          // Fallback: Try to focus on first carousel regardless of data availability
          const fallbackCarousel = `${carouselOrder[0]}-0`; // "Recent History-0"
          try {
            setFocus(fallbackCarousel);
          } catch {
            // If that fails, focus on the page itself
            focusSelf();
          }
        }
      }, 300); // Delay to ensure carousels are rendered

      return () => clearTimeout(timer);
    }
  }, [isLoadingInitialData, firstAvailableCarousel]); // eslint-disable-line

  const onAssetPress = useCallback(
    (asset: { gameSlug?: string }) => {
      navigate(`${gameInterstitialRoute}/${asset?.gameSlug}`);
    },
    [navigate]
  );

  const onRowFocus = useCallback(
    ({ y }: { y: number }) => {
      (ref.current as HTMLDivElement)?.scrollTo({
        top: y,
        behavior: 'smooth',
      });
    },
    [ref]
  );

  const onHeroFocus = useCallback(
    ({ y }: { y: number }) => {
      console.log('Hero focus triggered, scrolling to top');
      if (ref.current) {
        ref.current.scrollTo({
          top: y,
          behavior: 'smooth',
        });
      }
    },
    [ref]
  );

  const getCurrentData = () => {
    // Priority 1: If we have focused card data, use it to create dynamic data
    if (focusedCardData) {
      switch (focusedCardData.contentType) {
        case ContentType.GAME:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            gameTitle: focusedCardData?.name || focusedCardData?.title || 'Unknown Game',
            bannerUrl:
              focusedCardData?.bannerUrl ||
              focusedCardData?.imageUrl ||
              focusedCardData?.thumbnailUrl,
            contentType: ContentType.GAME,
            stats: {
              players: (focusedCardData?.playCount as string) || '2.1M',
              followers: (focusedCardData?.followerCount as string) || '850K',
              likes: (focusedCardData?.favoriteCount as string) || '1.2M',
              rating:
                (focusedCardData?.esrbRatingName as string) ||
                (focusedCardData?.esrbRating as string) ||
                'Not Rated',
            },
            genres: (focusedCardData?.genres as string[]) || [],
            onPlayClick: () =>
              console.log(
                'Play focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
            onMoreInfoClick: () =>
              console.log(
                'More info for focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
          };
        case ContentType.VIDEO:
          return {
            gameTitle:
              (focusedCardData?.title as string) ||
              (focusedCardData?.name as string) ||
              'Unknown Video',
            bannerUrl:
              (focusedCardData?.thumbnailUrl as string) || (focusedCardData?.imageUrl as string),
            contentType: ContentType.VIDEO,
            uploadDate: focusedCardData?.uploadDate as string,
            duration: focusedCardData?.duration as number,
            publisherInfo: {
              imageUrl: (focusedCardData?.channelLogo as string) || '',
              publisherName: (focusedCardData?.channelName as string) || 'Unknown Publisher',
              isVerified: focusedCardData?.isVerified || false,
            },
            stats: {
              uploadTime: (focusedCardData?.timeAgo as string) || 'Recently',
              views: (focusedCardData?.playCount as string) ?? '0',
              likes: (focusedCardData?.favoriteCount as string) ?? '0',
            },
            onPlayClick: () =>
              console.log('Play focused video clicked:', focusedCardData?.title as string),
            onSaveClick: () =>
              console.log('Save focused video clicked:', focusedCardData?.title as string),
          };
        case ContentType.EVENT:
        case ContentType.OFFER:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            eventTitle: focusedCardData?.title ?? 'Unknown Event',
            bannerUrl:
              (focusedCardData?.imageUrl as string) || (focusedCardData?.thumbnailUrl as string),
            contentType: ContentType.EVENT,
            stats: {
              dropDate: focusedCardData?.startDate
                ? formatDateTime(focusedCardData.startDate as string)
                : '12/10/24',
              expires: focusedCardData?.endDate
                ? formatDateTime(focusedCardData.endDate as string)
                : '01/15/25',
              views: (focusedCardData?.playCount as string) || '89K',
              saves: (focusedCardData?.favoriteCount as string) || '12K',
            },
            onMoreInfoClick: () =>
              console.log(
                'More info for focused event clicked:',
                focusedCardData?.companyName as string
              ),
            onSaveClick: () =>
              console.log('Save focused event clicked:', focusedCardData?.companyName as string),
          };
        default:
          return gameData;
      }
    }

    // Priority 2: If we have initial hero data, use it
    if (initialHeroData) {
      return initialHeroData;
    }

    // Priority 3: Fallback to static data when no focused card data or initial data
    return gameData;
  };

  return (
    <>
      {isLoadingInitialData ? (
        <Row width="100%" height="100vh" align="center" justify="center">
          <CircularLoader size={75} />
        </Row>
      ) : (
        <StickyHeroWrapper
          // ref={ref}
          heroSection={<LibraryOmbre data={getCurrentData()} onFocus={onHeroFocus} />}
          scrollableSection={
            <ContentContainer ref={ref}>
              {/* Carousel Rows */}

              <CarouselRow
                title="Recent History"
                handleApiCall={async page => {
                  return await getRecentHistory(page, PAGE_LIMIT);
                }}
                renderItem={(item, index) => (
                  <GameTile
                    gameSlug={item?.game_slug}
                    index={index}
                    name={item.title ?? ''}
                    imageUrl={item.image_url}
                    onEnterPress={onAssetPress}
                    esrbRating={item?.esrb}
                    onCardFocused={props => {
                      console.log('Recent History GameTile onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.GAME,
                        esrbRatingName: item?.esrb_description || undefined,
                        playCount: item?.play_count || undefined,
                        followerCount: item?.follower_count || undefined,
                        favoriteCount: item?.favorite_count || undefined,
                        genres: item?.genres || undefined,
                        gameLogoUrl: item?.logo_url ?? null,
                      };

                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={onRowFocus}
              />

              <CarouselRow
                title="Favorited Games"
                handleApiCall={async page => {
                  return await getFavoriteGames(page, PAGE_LIMIT);
                }}
                renderItem={(item, index) => (
                  <GameTile
                    gameSlug={item?.game_slug}
                    videoUrl={
                      item?.default_banner_media_type === 'VIDEO'
                        ? item?.default_banner_media_url
                        : ''
                    }
                    index={index}
                    name={item.title ?? ''}
                    imageUrl={item.image_url}
                    onEnterPress={onAssetPress}
                    esrbRating={item?.esrb}
                    onCardFocused={props => {
                      console.log('Favorited Games GameTile onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        gameLogoUrl: item?.logo_url ?? null,
                        bannerUrl: item?.default_banner_media_url ?? item?.image_url,
                        contentType: ContentType.GAME,
                        esrbRatingName: item?.esrb_description || undefined,
                        playCount: item?.play_count || undefined,
                        followerCount: item?.follower_count || undefined,
                        favoriteCount: item?.favorite_count || undefined,
                        genres: item?.genres || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={onRowFocus}
              />
              <CarouselRow
                title="Saved Games"
                handleApiCall={async page => {
                  return await getSavedGames(page, PAGE_LIMIT);
                }}
                renderItem={(item, index) => (
                  <GameTile
                    gameSlug={item?.game_slug}
                    videoUrl={
                      item?.default_banner_media_type === 'VIDEO'
                        ? item?.default_banner_media_url
                        : ''
                    }
                    index={index}
                    name={item.title ?? ''}
                    imageUrl={item.image_url}
                    onEnterPress={onAssetPress}
                    esrbRating={item?.esrb}
                    onFocus={(layout, props) => {
                      console.log('Saved Games GameTile focused - Props:', props);
                    }}
                    onCardFocused={props => {
                      console.log('Saved Games GameTile onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        gameLogoUrl: item?.logo_url ?? null,
                        contentType: ContentType.GAME,
                        esrbRatingName: item?.esrb_description || undefined,
                        playCount: item?.play_count || undefined,
                        followerCount: item?.follower_count || undefined,
                        favoriteCount: item?.favorite_count || undefined,
                        genres: item?.genres || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={onRowFocus}
              />

              <CarouselRow
                title="Favorited Videos"
                handleApiCall={async page => {
                  return await getFavoriteVideos(page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <VideoTile
                    isVerified={item?.is_verified}
                    videoUrl={item?.url}
                    channelLogo={item?.company_image}
                    duration={item?.duration}
                    thumbnailUrl={item?.thumbnail_url}
                    channelName={item?.company_name}
                    key={item?.media_id}
                    timeAgo={item?.created_at}
                    title={item?.title}
                    onEnterPress={onAssetPress}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      console.log('Favorited Videos VideoTile onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.VIDEO,
                        uploadDate: item?.created_at || undefined,
                        channelName: item?.company_name || undefined,
                        channelLogo: item?.company_image || undefined,
                        isVerified: item?.is_verified || false,
                        duration: item?.duration,
                        playCount: (item?.play_count as string) || undefined,
                        favoriteCount: (item?.favorite_count as string) || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="VIDEO_TILE"
              />
              <CarouselRow
                title="Saved Videos"
                handleApiCall={async page => {
                  return await getSavedVideos(page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <VideoTile
                    isVerified={item?.is_verified}
                    videoUrl={item?.url}
                    channelLogo={item?.company_image}
                    duration={item?.duration}
                    thumbnailUrl={item?.thumbnail_url}
                    channelName={item?.company_name}
                    key={item?.media_id}
                    timeAgo={item?.created_at}
                    title={item?.title}
                    onEnterPress={onAssetPress}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      console.log('Saved Videos VideoTile onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.VIDEO,
                        uploadDate: item?.created_at || undefined,
                        channelName: item?.company_name || undefined,
                        channelLogo: item?.company_image || undefined,
                        isVerified: item?.is_verified || false,
                        duration: item?.duration,
                        playCount: (item?.play_count as string) || undefined,
                        favoriteCount: (item?.favorite_count as string) || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="VIDEO_TILE"
              />
              <CarouselRow
                title="Saved Events"
                handleApiCall={async page => {
                  return await getSavedEvents(page, PAGE_LIMIT);
                }}
                refetchTrigger={refetchSavedEvents}
                onRefetchComplete={() => setRefetchSavedEvents(false)}
                renderItem={item => (
                  <PromoCard
                    initialIsSaved={true}
                    eventId={item?.event_id}
                    companyName={item?.company_name ?? ''}
                    companyUrl={item?.company_image ?? ''}
                    isVerified={item?.is_verified ?? false}
                    imageUrl={item?.image_url}
                    key={item?.event_id}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      console.log('Saved Events PromoCard onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.EVENT,
                        startDate: item?.start_date || undefined,
                        endDate: item?.end_date || undefined,
                        title: item?.title,
                        playCount: item?.play_count || undefined,
                        favoriteCount: item?.favorite_count || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                    onSaveToggle={() => {
                      // triggering refetch
                      setRefetchSavedEvents(true);
                    }}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="PROMO_CARD"
              />
              <CarouselRow
                title="Saved Offers"
                handleApiCall={async page => {
                  return await getSavedOffers(page, PAGE_LIMIT);
                }}
                refetchTrigger={refetchSavedOffers}
                onRefetchComplete={() => setRefetchSavedOffers(false)}
                renderItem={item => (
                  <PromoCard
                    initialIsSaved={true}
                    offerId={item?.offer_id}
                    companyName={item?.company_name ?? ''}
                    companyUrl={item?.company_image ?? ''}
                    isVerified={item?.is_verified ?? false}
                    imageUrl={item?.image_url}
                    key={item?.offer_id}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      console.log('Saved Offers PromoCard onCardFocused - Props:', props, item);
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.OFFER,
                        startDate: item?.start_date || undefined,
                        endDate: item?.end_date || undefined,
                        title: item?.title,
                        playCount:
                          ((item as unknown as Record<string, unknown>)?.play_count as string) ||
                          undefined,
                        favoriteCount:
                          ((item as unknown as Record<string, unknown>)
                            ?.favorite_count as string) || undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                    onSaveToggle={() => {
                      // triggering refetch
                      setRefetchSavedOffers(true);
                    }}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="VIDEO_TILE"
              />
              <CarouselRow
                title="Saved Images"
                handleApiCall={async page => {
                  return await getSavedContent(GAME_CONTENT.IMAGE, page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <ImageCard
                    imageUrl={item?.thumbnail_url ?? item?.url}
                    key={item?.media_id}
                    onFocus={() => {}}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="IMAGE_CARD"
              />
              <CarouselRow
                title="Favorited Images"
                handleApiCall={async page => {
                  return await getFavoriteContent(GAME_CONTENT.IMAGE, page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <ImageCard
                    imageUrl={item?.thumbnail_url ?? item?.url}
                    key={item?.media_id}
                    onFocus={() => {}}
                  />
                )}
                onFocus={onRowFocus}
                skeletonVariant="IMAGE_CARD"
              />
            </ContentContainer>
          }
        />

        // </FixedHeroWrapper>
      )}
    </>
  );
};

export default LibraryPage;
