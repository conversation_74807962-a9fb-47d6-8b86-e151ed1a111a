import styled from 'styled-components';

export const GameInterstitialWrap = styled.div`
  position: relative;
  width: 100%;
`;

export const IntInfoWrap = styled.div`
  position: absolute;
  left: 32px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
`;

export const VideoWrap = styled.div`
  position: absolute;
  inset: 0px;
`;

export const GradientOverlay = styled.div`
  position: absolute;
  inset: 0;
  background: ${props => props.theme?.interstitialOverlayGradient};
`;
