import { useCallback, useEffect, useState } from 'react';
import { Col, Row } from 'styles/sharedStyles';
import { BodyXL } from 'styles/theme/typography';
import { FaLightbulb, FaHeart } from 'react-icons/fa';
import { IoMdAddCircle } from 'react-icons/io';
import { AiFillMinusCircle } from 'react-icons/ai';
import { CiHeart } from 'react-icons/ci';
import { GiGamepad } from 'react-icons/gi';
import { GameBadge, IGameBadge } from 'shared/gameBadge';
import { IconButton, PrimaryButton } from 'shared/buttons';
import {
  checkLikeFollowGameStatus,
  favoriteSaveGame,
  followGame,
  unFollowGame,
} from 'api/game-service/game-service.api';
import { useNavigate } from 'react-router-dom';
import { gameDetailsRoute } from 'routes/path';
import toast from 'react-hot-toast';

interface IInterstitialInfo extends Partial<IGameBadge> {
  description?: string;
  gameName?: string;
  id?: string;
  onFollow?: () => Promise<void> | void;
  onLike?: () => Promise<void> | void;
}

export const InterstitialInfo = (props: IInterstitialInfo) => {
  const { description, id, gameName, onFollow, onLike, ...rest } = props;

  const navigate = useNavigate();

  const [isFavorite, setIsFavorite] = useState(false);
  const [isFavoriteLoading, setIsFavoriteLoading] = useState(false);

  const [isFollow, setIsFollow] = useState(false);
  const [isFollowLoading, setIsFollowLoading] = useState(false);

  const fetchUserGameStatus = useCallback(async () => {
    if (id) {
      checkLikeFollowGameStatus(id).then(res => {
        setIsFavorite(res.is_favourite);
        setIsFollow(res.is_follow);
      });
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchUserGameStatus();
    }
  }, [id, fetchUserGameStatus]);

  const handlePlay = () => {
    // todo
  };

  const handleMoreInfo = () => {
    navigate(`${gameDetailsRoute}/${id}`);
  };

  const handleFollow = async () => {
    if (!id || isFollowLoading) return;
    try {
      setIsFollowLoading(true);
      if (isFollow) {
        await unFollowGame(id);
      } else {
        await followGame(id);
      }

      toast.success(`${gameName ?? 'Game'} ${isFollow ? 'unfollowed' : 'followed'} successfully`);

      await onFollow?.();

      await fetchUserGameStatus();
    } catch (error) {
      console.error(error);
    } finally {
      setIsFollowLoading(false);
    }
  };

  const handleFavorite = async () => {
    if (!id || isFavoriteLoading) return;
    try {
      setIsFavoriteLoading(true);
      await favoriteSaveGame(id, !isFavorite);

      toast.success(
        `${gameName ?? 'Game'} is  ${!isFavorite ? 'added to' : 'removed from'} favorite successfully`
      );

      await onLike?.();

      await fetchUserGameStatus();
    } catch (error) {
      console.error(error);
    } finally {
      setIsFavoriteLoading(false);
    }
  };

  return (
    <Col gap="40px" width="100%">
      <GameBadge
        {...rest}
        imageStyle={{
          height: '220px',
        }}
      />

      <Row maxWidth="1004px">
        <BodyXL>{description}</BodyXL>
      </Row>

      <Row gap="32px" align="center">
        <PrimaryButton focusKey="play-key" onClick={handlePlay} icon={<GiGamepad fontSize={48} />}>
          Play
        </PrimaryButton>
        <PrimaryButton
          focusKey="mode-info-key"
          onClick={handleMoreInfo}
          icon={<FaLightbulb fontSize={48} />}
        >
          More Info
        </PrimaryButton>
        <PrimaryButton
          focusKey="follow-key"
          onClick={handleFollow}
          isLoading={isFollowLoading}
          icon={isFollow ? <AiFillMinusCircle fontSize={48} /> : <IoMdAddCircle fontSize={48} />}
        >
          {isFollow ? 'Unfollow' : 'Follow'}
        </PrimaryButton>
        <IconButton
          focusKey="favorite-key"
          onClick={handleFavorite}
          isLoading={isFavoriteLoading}
          icon={isFavorite ? <FaHeart fontSize={44} /> : <CiHeart fontSize={44} />}
        />
      </Row>
    </Col>
  );
};
