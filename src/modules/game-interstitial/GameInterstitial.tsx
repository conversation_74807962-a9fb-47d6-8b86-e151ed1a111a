import { GameInterstitialWrap, GradientOverlay, IntInfoWrap, VideoWrap } from './style';
import { InterstitialInfo } from './components/InterstitialInfo';
import { ImageVideo } from 'shared/imageVideo';
import { useParams } from 'react-router-dom';
import { useCallback, useEffect, useState } from 'react';
import { getGameDetails } from 'api/game-service/game-service.api';
import { IGame } from 'types/api/game/details.game.api.types';
import { FocusContext, setFocus, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { CircularLoader, Row } from 'styles/sharedStyles';

export const GameInterstitial = () => {
  const { focusKey, focusSelf, ref } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: 'play-key',
  });
  const { id } = useParams<{ id: string }>();

  const [isLoading, setIsLoading] = useState(false);
  const [gameDetails, setGameDetails] = useState<IGame>();

  const fetchGameData = useCallback(async () => {
    if (!id) return;

    try {
      const res = await getGameDetails(id);
      setGameDetails(res);
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      setIsLoading(true);
      fetchGameData().finally(() => setFocus('play-key'));
    }
  }, [id, fetchGameData]);

  useEffect(() => {
    const timer = setTimeout(() => {
      focusSelf();
    }, 100);

    return () => clearTimeout(timer);
  }, [focusSelf]);

  return (
    <FocusContext.Provider value={focusKey}>
      {isLoading ? (
        <Row ref={ref} width="100%" height="100%" align="center" justify="center">
          <CircularLoader size={75} />
        </Row>
      ) : (
        <GameInterstitialWrap ref={ref}>
          <IntInfoWrap>
            <InterstitialInfo
              description={gameDetails?.description}
              imageUrl={gameDetails?.game_text_img_url}
              themes={gameDetails?.game_theme}
              rating={gameDetails?.esrb_description}
              id={id}
              gameName={gameDetails?.game_title}
              followers={gameDetails?.followers_count}
              likes={gameDetails?.favorite_count}
              players={gameDetails?.player_count}
              onFollow={() => fetchGameData()}
              onLike={() => fetchGameData()}
            />
          </IntInfoWrap>
          <VideoWrap>
            <ImageVideo
              autoPlay
              height={'100%'}
              width={'100%'}
              imageUrl={gameDetails?.thumbnail}
              videoUrl={gameDetails?.trailers?.[0]?.url}
            />
          </VideoWrap>

          <GradientOverlay />
        </GameInterstitialWrap>
      )}
    </FocusContext.Provider>
  );
};
