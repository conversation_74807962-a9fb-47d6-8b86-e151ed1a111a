import {
  FocusContext,
  useFocusable,
  type FocusableComponentLayout,
} from '@noriginmedia/norigin-spatial-navigation';
import React, { useState, useEffect } from 'react';
import { AiFillMinusCircle } from 'react-icons/ai';
import { IoMdAddCircle } from 'react-icons/io';
import { PrimaryButton } from 'shared/buttons';
import { Row } from 'styles/sharedStyles';
import { FocusedCardData } from 'types/focused-card.types';
import { checkIsPubFollowed, followPub, unFollowPub } from 'api/user-service/user-service.api';

interface FolloSectionProps {
  publisherId: string;
  onRowFocus: (layout?: FocusableComponentLayout) => void;
  setFocusedCardData: React.Dispatch<React.SetStateAction<FocusedCardData | null>>;
}

const FollowSection: React.FC<FolloSectionProps> = ({
  publisherId,
  onRowFocus,
  setFocusedCardData,
}) => {
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFollowLoading, setIsFollowLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Check initial follow status
  useEffect(() => {
    const checkFollowStatus = async () => {
      setIsInitialLoading(true);
      try {
        const response = await checkIsPubFollowed(publisherId);
        setIsFollowing(response.isFollowing);
      } catch (error) {
        console.error('Error checking follow status:', error);
      } finally {
        setIsInitialLoading(false);
      }
    };

    if (publisherId) {
      checkFollowStatus();
    }
  }, [publisherId]);

  // Handle follow/unfollow action
  const handleFollow = async () => {
    if (isFollowLoading) return;

    setIsFollowLoading(true);
    try {
      if (isFollowing) {
        await unFollowPub(publisherId);
        setIsFollowing(false);
      } else {
        await followPub(publisherId);
        setIsFollowing(true);
      }
    } catch (error) {
      console.error('Error updating follow status:', error);
    } finally {
      setIsFollowLoading(false);
    }
  };

  const { ref, focusKey } = useFocusable({
    focusable: true,
    saveLastFocusedChild: true,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
    focusKey: 'FollowSection',
    preferredChildFocusKey: 'follow-button-key',
  });

  return (
    <FocusContext.Provider value={focusKey}>
      <Row ref={ref} marginTop="24px">
        <PrimaryButton
          focusKey="follow-button-key"
          onClick={handleFollow}
          isLoading={isFollowLoading ?? isInitialLoading}
          onFocus={layout => {
            onRowFocus(layout);
            setFocusedCardData(null);
          }}
          icon={isFollowing ? <AiFillMinusCircle fontSize={48} /> : <IoMdAddCircle fontSize={48} />}
          disabled={isInitialLoading}
        >
          {isFollowing ? 'Following' : 'Follow'}
        </PrimaryButton>
      </Row>
    </FocusContext.Provider>
  );
};

export default FollowSection;
