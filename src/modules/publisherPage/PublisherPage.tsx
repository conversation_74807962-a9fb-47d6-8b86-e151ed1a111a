import {
  FocusableComponentLayout,
  FocusContext,
  useFocusable,
} from '@noriginmedia/norigin-spatial-navigation';
import {
  getPubEvents,
  getPubGameGenre,
  getPubGames,
  getPubHero,
  getPubLatestUpdates,
  getPublisherStats,
  getPubOffers,
} from 'api/game-service/game-service.api';
import { getUserById } from 'api/user-service/user-service.api';
import { PAGE_LIMIT } from 'config/app.config';
import { BannerType } from 'enums/banner-type.enums';
import { ContentType } from 'enums/content-type.enums';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { gameInterstitialRoute } from 'routes/path';
import StandardBanner from 'shared/banner2/standard/StandardBanner';
import LibraryOmbre from 'shared/banners/ombre/LibraryOmbre';
import CarouselRow from 'shared/carouselRow/CarouselRow';
import { CategoryCard } from 'shared/category-card/CategoryCard';
import StickyHeroWrapper from 'shared/fixedHeroWrapper/StickyHeroWrapper';

import { GameTile } from 'shared/game-tile/GameTile';
import { PromoCard } from 'shared/promo-card/PromoCard';
import { VideoTile } from 'shared/video-tile/VideoTile';
import styled from 'styled-components';
import { CircularLoader, Col, Row } from 'styles/sharedStyles';
import { IProfile } from 'types/api/user/profile.user.api.types';
import { GameBannerItem } from 'types/banner.types';
import { FocusedCardData } from 'types/focused-card.types';
import FolloSection from './FollowSection';

const ContentContainer = styled(Col)`
  padding: 0 32px;
  color: ${({ theme }) => theme.textPrimary};
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  background: ${({ theme }) => theme.gameTileOverlayGradient};
  gap: 48px;
  padding-bottom: 100px;
`;

export const PublisherPage = () => {
  const { id = '7cd79ab4-8fee-4e93-a997-3015823f25a5' } = useParams<{ id: string }>();

  const [profile, setProfile] = useState<IProfile | null>(null);
  const [publisherStats, setPublisherStats] = useState<{
    followers?: number;
    games?: number;
    upcomingEvents?: number;
  } | null>(null);
  const [publisherHeroData, setPublisherHeroData] = useState<GameBannerItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const navigate = useNavigate();

  // Refetch trigger states for events and offers
  const [refetchSavedEvents, setRefetchSavedEvents] = useState(false);
  const [refetchSavedOffers, setRefetchSavedOffers] = useState(false);
  const [focusedCardData, setFocusedCardData] = useState<FocusedCardData | null>(null);

  // Define carousel order for focus priority

  const getCurrentBannerData = useCallback(() => {
    // If we have focused card data, create data for LibraryOmbre
    if (focusedCardData) {
      switch (focusedCardData.contentType) {
        case ContentType.GAME:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            gameTitle: focusedCardData?.name || focusedCardData?.title || 'Unknown Game',
            bannerUrl:
              focusedCardData?.bannerUrl ||
              focusedCardData?.imageUrl ||
              focusedCardData?.thumbnailUrl,
            contentType: ContentType.GAME,
            stats: {
              players: focusedCardData?.playCount ?? 0,
              followers: focusedCardData?.followerCount ?? 0,
              likes: focusedCardData?.favoriteCount ?? 0,
              rating:
                (focusedCardData?.esrbRatingName as string) ||
                (focusedCardData?.esrbRating as string) ||
                'Not Rated',
            },
            genres: (focusedCardData?.genres as string[]) || [],
            onPlayClick: () =>
              console.log(
                'Play focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
            onMoreInfoClick: () =>
              console.log(
                'More info for focused game clicked:',
                focusedCardData?.name || focusedCardData?.title
              ),
          };
        case ContentType.VIDEO:
          return {
            gameTitle:
              (focusedCardData?.title as string) ||
              (focusedCardData?.name as string) ||
              'Unknown Video',
            bannerUrl:
              (focusedCardData?.thumbnailUrl as string) || (focusedCardData?.imageUrl as string),
            contentType: ContentType.VIDEO,
            uploadDate: focusedCardData?.uploadDate as string,
            duration: focusedCardData?.duration as number,
            publisherInfo: {
              imageUrl: (focusedCardData?.channelLogo as string) || '',
              publisherName: (focusedCardData?.channelName as string) || 'Unknown Publisher',
              isVerified: focusedCardData?.isVerified || false,
            },
            stats: {
              uploadTime:
                (focusedCardData?.uploadDate as string) ||
                (focusedCardData?.timeAgo as string) ||
                'Recently',
              views: focusedCardData?.playCount ?? 0,
              likes: focusedCardData?.favoriteCount ?? 0,
            },
            onPlayClick: () =>
              console.log('Play focused video clicked:', focusedCardData?.title as string),
            onSaveClick: () =>
              console.log('Save focused video clicked:', focusedCardData?.title as string),
          };
        case ContentType.EVENT:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            eventTitle: focusedCardData?.title ?? focusedCardData?.name ?? 'Unknown Event',
            bannerUrl:
              focusedCardData?.bannerUrl ??
              focusedCardData?.imageUrl ??
              focusedCardData?.thumbnailUrl,
            contentType: ContentType.EVENT,
            stats: {
              dropDate: focusedCardData?.startDate ?? '',
              expires: focusedCardData?.endDate ?? undefined,
              views: focusedCardData?.playCount ?? 0,
              saves: focusedCardData?.favoriteCount ?? 0,
            },
            onMoreInfoClick: () =>
              console.log(
                'More info for focused event clicked:',
                focusedCardData?.title || focusedCardData?.name
              ),
            onSaveClick: () =>
              console.log(
                'Save focused event clicked:',
                focusedCardData?.title || focusedCardData?.name
              ),
          };
        case ContentType.OFFER:
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl ?? '',
            eventTitle: focusedCardData?.title ?? focusedCardData?.name ?? 'Unknown Offer',
            bannerUrl:
              focusedCardData?.bannerUrl ??
              focusedCardData?.imageUrl ??
              focusedCardData?.thumbnailUrl,
            contentType: ContentType.OFFER,
            stats: {
              dropDate: focusedCardData?.startDate ?? 'TBD',
              expires: focusedCardData?.endDate ?? undefined,
              views: focusedCardData?.playCount ?? 0,
              saves: focusedCardData?.favoriteCount ?? 0,
            },
            onMoreInfoClick: () =>
              console.log(
                'More info for focused offer clicked:',
                focusedCardData?.title ?? focusedCardData?.name
              ),
            onSaveClick: () =>
              console.log(
                'Save focused offer clicked:',
                focusedCardData?.title ?? focusedCardData?.name
              ),
          };
        default:
          // Return a default game data structure for unsupported types
          return {
            gameLogoUrl: focusedCardData?.gameLogoUrl,
            gameTitle: 'Unknown Content',
            bannerUrl: focusedCardData?.imageUrl ?? focusedCardData?.thumbnailUrl,
            contentType: ContentType.GAME,
            stats: {
              players: '0',
              followers: '0',
              likes: '0',
              rating: 'Not Rated',
            },
            genres: [],
            onPlayClick: () => console.log('Play clicked'),
            onMoreInfoClick: () => console.log('More info clicked'),
          };
      }
    }
    return null;
  }, [focusedCardData]);

  const { ref, focusSelf, focusKey } = useFocusable({
    focusable: true,
    saveLastFocusedChild: true,
    trackChildren: true,
    autoRestoreFocus: true,
    isFocusBoundary: false,
    focusKey: 'publisherPage',
    preferredChildFocusKey: 'nav-dot-0',
  });

  const onAssetPress = useCallback(
    (asset: { gameSlug?: string }) => {
      navigate(`${gameInterstitialRoute}/${asset?.gameSlug}`);
    },
    [navigate]
  );

  const onRowFocus = useCallback(
    (layout?: FocusableComponentLayout, extraValue: number = 0) => {
      if (layout) {
        (ref.current as HTMLDivElement)?.scrollTo({
          top: layout.y + extraValue,
          behavior: 'smooth',
        });
      }
    },
    [ref]
  );

  const fetchProfile = async (id: string) => {
    try {
      const res = await getUserById(id);
      setProfile(res);
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      setProfile(null);
    }
  };

  const GetPubHero = async (id: string) => {
    try {
      const res = await getPubHero(id);

      // Sort by display_order to ensure proper banner sequence
      const sortedData = (res.data || []).sort(
        (a: unknown, b: unknown) =>
          (a as GameBannerItem).display_order - (b as GameBannerItem).display_order
      );
      setPublisherHeroData(sortedData as GameBannerItem[]);
    } catch (error) {
      console.error('Failed to fetch publisher hero:', error);
      setPublisherHeroData([]);
    }
  };

  const GetPublisherStats = async (id: string) => {
    if (!id) return;
    try {
      const res = await getPublisherStats(id);
      setPublisherStats({
        followers: res.followers_count ?? 0,
        games: res.games_count ?? 0,
        upcomingEvents: res.upcoming_events ?? 0,
      });
    } catch (error) {
      console.error('Failed to fetch publisher stats:', error);
      setPublisherStats({
        followers: 0,
        games: 0,
        upcomingEvents: 0,
      });
    }
  };

  const loadAllData = async (id: string) => {
    setIsLoading(true);
    try {
      await Promise.all([fetchProfile(id), GetPublisherStats(id), GetPubHero(id)]);
    } catch (error) {
      console.error('Failed to load publisher data:', error);
    } finally {
      setIsLoading(false);
      // Set focus after loading is complete and DOM is updated
      setTimeout(() => {
        focusSelf();
      }, 100);
    }
  };

  // Convert publisher hero data to StandardBanner format (similar to GameDetails)
  const publisherProfileData = {
    id: 'publisher-profile',
    title:
      profile?.user?.display_name || profile?.user?.company_details?.company_name || 'Publisher',
    media_url: profile?.user?.cover_image_url ?? profile?.user?.company_details?.image ?? '',
    media_type: ContentType.PUBLISHER,
    game_slug: '',
    game_id: '',
    trailer_banner: [] as Array<{
      id: string;
      media_url: string;
      start_date: string;
      end_date: string | null;
      title: string;
      is_saved_event?: boolean;
      is_saved_offer?: boolean;
    }>,
    offer_banner: [] as Array<{
      id: string;
      media_url: string;
      start_date: string;
      end_date: string | null;
      title: string;
      is_saved_event?: boolean;
      is_saved_offer?: boolean;
    }>,
    event_banner: [] as Array<{
      id: string;
      media_url: string;
      start_date: string;
      end_date: string | null;
      title: string;
      is_saved_event?: boolean;
      is_saved_offer?: boolean;
    }>,
    modified_by: 'system',
    created_at: new Date().toISOString(),
    display_order: -1, // Ensure it appears first
    gameLogoUrl: undefined,
    gameTitle:
      profile?.user?.display_name || profile?.user?.company_details?.company_name || 'Publisher',
    contentData: {
      publisherName:
        profile?.user?.display_name || profile?.user?.company_details?.company_name || 'Publisher',
      publisherLogoUrl: profile?.user?.company_details?.image || profile?.user?.dp_url,
      publisherLogoText:
        profile?.user?.company_details?.company_name?.split(' ').slice(0, 2).join('\n') ||
        'PUBLISHER',
      isVerified: profile?.user?.is_verified || false,
      stats: {
        followers: publisherStats?.followers || 0,
        games: publisherStats?.games || 0,
        upcomingEvents: publisherStats?.upcomingEvents || 0,
      },
    },
    imageUrl: '',
  };

  const convertedPublisherBannerData = [
    publisherProfileData,
    ...publisherHeroData.map(item => ({
      id: item.id,
      title: item.title,
      media_url: item.media_url ?? '',
      media_type: item.media_type ?? 'IMAGE',
      game_slug: item.game_slug ?? '',
      game_id: item.game_id ?? '',
      trailer_banner: (item.trailer_banner ?? []) as Array<{
        id: string;
        media_url: string;
        start_date: string;
        end_date: string | null;
        title: string;
        is_saved_event?: boolean;
        is_saved_offer?: boolean;
      }>,
      offer_banner: (item.offer_banner ?? []) as Array<{
        id: string;
        media_url: string;
        start_date: string;
        end_date: string | null;
        title: string;
        is_saved_event?: boolean;
        is_saved_offer?: boolean;
      }>,
      event_banner: (item.event_banner ?? []) as Array<{
        id: string;
        media_url: string;
        start_date: string;
        end_date: string | null;
        title: string;
        is_saved_event?: boolean;
        is_saved_offer?: boolean;
      }>,
      modified_by: item.modified_by ?? '',
      created_at: item.created_at ?? new Date().toISOString(),
      display_order: item.display_order || 0,
      // Additional properties for component data
      gameLogoUrl: undefined, // Publisher pages might not have game logos
      gameTitle: item.title || 'Publisher Content',
      contentData: {
        gameLogoUrl: undefined,
        gameTitle: item.title || 'Publisher Content',
        stats: {
          playCount: item?.play_count ?? 0,
          followers: item?.follower_count ?? 0,
          likes: item?.favorite_count ?? 0,
          saveCount: item?.saved_count ?? 0,
          rating: 'E for Everyone',
        },
        genres: item?.genres || [],
      },
      // For backward compatibility
      imageUrl: item.media_url ?? '',
    })),
  ];

  useEffect(() => {
    if (id) {
      loadAllData(id);
    }
  }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <FocusContext.Provider value={focusKey}>
      {isLoading ? (
        <Row width="100%" height="100vh" align="center" justify="center">
          <CircularLoader size={75} />
        </Row>
      ) : (
        <StickyHeroWrapper
          customTransformValue="0"
          heroSection={
            focusedCardData && getCurrentBannerData() ? (
              <LibraryOmbre data={getCurrentBannerData()!} bannerType={BannerType.DYNAMIC} />
            ) : (
              <StandardBanner
                data={
                  convertedPublisherBannerData.length > 0 ? convertedPublisherBannerData : undefined
                }
                bannerType={BannerType.DYNAMIC}
              />
            )
            // <LibraryOmbre data={getCurrentData()} onFocus={onHeroFocus} />
          }
          scrollableSection={
            <ContentContainer ref={ref}>
              {/* Carousel Rows */}

              <FolloSection
                publisherId={id}
                onRowFocus={onRowFocus}
                setFocusedCardData={setFocusedCardData}
              />

              <CarouselRow
                title="Featured Games"
                handleApiCall={async page => {
                  return await getPubGames(id, page, PAGE_LIMIT, true);
                }}
                renderItem={(item, index) => (
                  <GameTile
                    gameSlug={item?.slug ?? ''}
                    index={index}
                    name={item.name ?? ''}
                    imageUrl={item.cover_url ?? ''}
                    videoUrl={item?.trailer_url ?? ''}
                    onEnterPress={onAssetPress}
                    esrbRating={item?.esrb_img_url ?? ''}
                    onCardFocused={props => {
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.GAME,
                        esrbRatingName: item?.esrb_description,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 0)}
              />

              <CarouselRow
                title="Latest Updates"
                handleApiCall={async page => {
                  return await getPubLatestUpdates(id, page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <VideoTile
                    isVerified={false}
                    videoUrl={item?.video_url ?? ''}
                    channelLogo={''}
                    duration={item?.duration ?? 0}
                    thumbnailUrl={item?.thumbnail_url ?? ''}
                    channelName={''}
                    key={item?.id}
                    timeAgo={item?.created_at ?? ''}
                    title={item?.title ?? ''}
                    onEnterPress={onAssetPress}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.VIDEO,
                        uploadDate: item?.created_at || undefined,
                        isVerified: false,
                        duration: item?.duration ?? undefined,
                        thumbnailUrl: item?.thumbnail_url ?? undefined,
                      };
                      setFocusedCardData(focusedData);
                    }}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 0)}
                skeletonVariant="VIDEO_TILE"
              />

              <CarouselRow
                title="Latest Events"
                handleApiCall={async page => {
                  return await getPubEvents(id, page, PAGE_LIMIT);
                }}
                refetchTrigger={refetchSavedEvents}
                onRefetchComplete={() => setRefetchSavedEvents(false)}
                renderItem={item => (
                  <PromoCard
                    initialIsSaved={true}
                    eventId={item?.id ?? ''}
                    companyName={''}
                    companyUrl={''}
                    isVerified={false}
                    imageUrl={item?.media_url ?? ''}
                    key={item?.id ?? ''}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.EVENT,
                        startDate: item?.start_date || undefined,
                        endDate: item?.end_date ?? undefined,
                        title: item?.title,
                        playCount: item?.play_count ?? 0,
                        favoriteCount: item?.favorite_count ?? 0,
                      };
                      setFocusedCardData(focusedData);
                    }}
                    onSaveToggle={() => {
                      // triggering refetch
                      setRefetchSavedEvents(true);
                    }}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 0)}
                skeletonVariant="PROMO_CARD"
              />
              <CarouselRow
                title="Latest Offers"
                handleApiCall={async page => {
                  return await getPubOffers(id, page, PAGE_LIMIT);
                }}
                refetchTrigger={refetchSavedOffers}
                onRefetchComplete={() => setRefetchSavedOffers(false)}
                renderItem={item => (
                  <PromoCard
                    initialIsSaved={true}
                    offerId={item?.id ?? ''}
                    companyName={''}
                    companyUrl={''}
                    isVerified={false}
                    imageUrl={item?.media_url ?? ''}
                    key={item?.id}
                    onFocus={() => {}}
                    onCardFocused={props => {
                      const focusedData: FocusedCardData = {
                        ...(props as object),
                        contentType: ContentType.OFFER,
                        startDate: item?.start_date ?? undefined,
                        endDate: item?.end_date ?? undefined,
                        playCount: item?.play_count ?? 0,
                        favoriteCount: item?.favorite_count ?? 0,
                      };
                      setFocusedCardData(focusedData);
                    }}
                    onSaveToggle={() => {
                      // triggering refetch
                      setRefetchSavedOffers(true);
                    }}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 0)}
                skeletonVariant="VIDEO_TILE"
              />

              <CarouselRow
                title="Genres"
                handleApiCall={async page => {
                  return await getPubGameGenre(id, page, PAGE_LIMIT);
                }}
                renderItem={item => (
                  <CategoryCard
                    imageUrl={item?.image_url ?? ''}
                    categoryName={item?.name ?? ''}
                    key={item?.slug}
                    onFocus={() => {}}
                  />
                )}
                onFocus={layout => onRowFocus(layout, 0)}
                skeletonVariant="CATEGORY_CARD"
              />
            </ContentContainer>
          }
        />

        // </FixedHeroWrapper>
      )}
    </FocusContext.Provider>
  );
};
