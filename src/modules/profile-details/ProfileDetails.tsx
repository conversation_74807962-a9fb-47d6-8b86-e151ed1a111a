import { useCallback, useEffect, useState, useRef } from 'react';
import { MainWrap, ProfileWrap } from './style';
import { ProfileHero } from './components/ProfileHero';
import CarouselRow from 'shared/carouselRow/CarouselRow';
import { GameTile } from 'shared/game-tile/GameTile';
import {
  getPlayerProfileSectionGames,
  getUserStatusWebSocket,
} from 'api/game-service/game-service.api';
import { PLAYER_PROFILE_SECTION } from 'enums/player-profile-section.enum';
import { useNavigate, useParams } from 'react-router-dom';
import { PAGE_LIMIT } from 'config/app.config';
import { FocusContext, useFocusable, setFocus } from '@noriginmedia/norigin-spatial-navigation';
import { gameDetailsRoute } from 'routes/path';
import {
  checkFriendStatus,
  getPlayerProfileStats,
  getUserById,
} from 'api/user-service/user-service.api';
import { IProfile } from 'types/api/user/profile.user.api.types';
import { useAppSelector } from 'hooks/redux.hooks';
import { CircularLoader, Col } from 'styles/sharedStyles';
import { FRIEND_STATUS } from 'enums/friend.enums';
import { IFriendStatsRes, IProfileStatsRes } from 'types/api/user/friend.user.api.types';
import { useWebSocket } from 'hooks/useWebSocket';
import {
  WEB_SOCKET_MESSAGE,
  WEB_SOCKET_SEND_EVENT,
  WEB_SOCKET_STATUS,
} from 'enums/socket-events.enum';

export const ProfileDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [prefKey, setPrefKey] = useState<string>();
  const [dataLoaded, setDataLoaded] = useState(false);
  const { ref, focusKey, focusSelf } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: prefKey,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfile] = useState<IProfile | null>(null);
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const [isFriends, setIsFriends] = useState(false);
  const [friendStatus, setFriendStatus] = useState<IFriendStatsRes>();
  const [profileStats, setProfileStats] = useState<IProfileStatsRes>();
  const [isOnline, setIsOnline] = useState(false);

  const { profile: userData, isOnline: isCurrentUserOnline } = useAppSelector(state => state?.user);
  const navigate = useNavigate();

  // Use ref to track if component is mounted to prevent state updates after unmount
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Memoize the websocket event handler
  const handleMyWebSocketOpen = useCallback(
    (event: MessageEvent) => {
      if (!isMountedRef.current) return;

      if (event?.data?.friend_id != null) {
        setIsOnline(event?.data?.is_online);
      }

      if (event?.data?.session_id) {
        emitWSEvent({
          message: WEB_SOCKET_MESSAGE.EVENT,
          event: WEB_SOCKET_SEND_EVENT.USER,
          user_id: id,
          status: WEB_SOCKET_STATUS.REGISTER,
        });
      }
    },
    [id]
  );

  const { connectSocket, disconnectSocket, emitWSEvent } = useWebSocket({
    socketURL: getUserStatusWebSocket(),
    onOpen: handleMyWebSocketOpen, // Now properly memoized
    autoConnect: false,
    disablePing: true,
  });

  // Focus management after data loads
  useEffect(() => {
    if (dataLoaded && !isLoading && profile && isFriends) {
      setPrefKey('Favorite Games-0');

      const timer = setTimeout(() => {
        if (!isMountedRef.current) return;
        try {
          setFocus('Favorite Games-0');
        } catch {
          focusSelf();
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [dataLoaded, isLoading, profile, isFriends, focusSelf]);

  // Separate function for fetching profile stats
  const fetchProfileStats = useCallback(async (userId: string) => {
    try {
      const res = await getPlayerProfileStats(userId);
      if (isMountedRef.current) {
        setProfileStats(res);
      }
    } catch (error) {
      console.log(error);
    }
  }, []);

  // Main profile fetch function - removed connectSocket from dependencies
  const fetchProfile = useCallback(async () => {
    if (!isMountedRef.current) return;

    try {
      setIsLoading(true);

      if (id) {
        // Fetching another user's profile
        const [profileData, friendStatusData] = await Promise.all([
          getUserById(id),
          checkFriendStatus(id),
        ]);

        if (!isMountedRef.current) return;

        setProfile(profileData);
        setFriendStatus(friendStatusData);
        setIsFriends(friendStatusData?.status === FRIEND_STATUS.ACCEPTED);
        setIsCurrentUser(false);

        await fetchProfileStats(id);
        connectSocket();
      } else {
        // Current user's profile
        if (!isMountedRef.current) return;

        setIsFriends(true);
        setProfile(userData);
        setIsCurrentUser(true);
        setIsOnline(true);

        if (userData?.user?.id) {
          await fetchProfileStats(userData.user.id);
        }
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
        setDataLoaded(true);
      }
    }
  }, [id, userData, connectSocket, fetchProfileStats]);

  // Separate effect for initial data fetch
  useEffect(() => {
    fetchProfile();
  }, [id, userData?.user?.id]); // Removed fetchProfile from dependencies

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (id) {
        emitWSEvent({
          message: WEB_SOCKET_MESSAGE.EVENT,
          event: WEB_SOCKET_SEND_EVENT.USER,
          user_id: id,
          status: WEB_SOCKET_STATUS.UNREGISTER,
        });
        disconnectSocket();
      }
    };
  }, [id, emitWSEvent, disconnectSocket]);

  // Memoize the onRowFocus callback
  const onRowFocus = useCallback(
    ({ y }: { y: number }) => {
      (ref.current as HTMLDivElement)?.scrollTo({
        top: y,
        behavior: 'smooth',
      });
    },
    [ref]
  );

  // Memoize the friend toggle handler
  const handleFriendToggle = useCallback(() => {
    fetchProfile();
  }, [fetchProfile]);

  return (
    <FocusContext.Provider value={focusKey}>
      {isLoading || !profile ? (
        <Col height="100%" width="100%" align="center" justify="center">
          <CircularLoader size={75} />
        </Col>
      ) : (
        <MainWrap ref={ref}>
          <ProfileHero
            friendReq={friendStatus}
            profile={profile}
            profileStats={profileStats}
            isCurrentUser={isCurrentUser}
            onFriendToggle={handleFriendToggle}
            isOnline={id ? isOnline : isCurrentUserOnline}
          />
          {profile?.user?.id && isFriends && (
            <ProfileWrap>
              <CarouselRow
                title="Favorite Games"
                handleApiCall={async page => {
                  return await getPlayerProfileSectionGames(
                    PLAYER_PROFILE_SECTION.FAVORITE_DESC,
                    profile?.user?.id,
                    page,
                    PAGE_LIMIT
                  );
                }}
                renderItem={(game, i) => (
                  <GameTile
                    key={game.name}
                    index={i}
                    name={game.name}
                    imageUrl={game.thumbnail ?? ''}
                    onEnterPress={() => navigate(`${gameDetailsRoute}/${game.slug}`)}
                    isVertical={true}
                    gameSlug={game?.slug}
                  />
                )}
                onFocus={onRowFocus}
              />
              {isCurrentUser && (
                <CarouselRow
                  title="Continue Playing"
                  handleApiCall={async page => {
                    return await getPlayerProfileSectionGames(
                      PLAYER_PROFILE_SECTION.CONTINUE_PLAYING_DESC,
                      profile?.user?.id,
                      page,
                      PAGE_LIMIT
                    );
                  }}
                  renderItem={(game, i) => (
                    <GameTile
                      key={game.name}
                      index={i}
                      name={game.name}
                      imageUrl={game.thumbnail ?? ''}
                      onEnterPress={() => navigate(`${gameDetailsRoute}/${game.slug}`)}
                      gameSlug={game?.slug}
                      esrbRating={game?.esrb_rating_img_url ?? ''}
                    />
                  )}
                  onFocus={onRowFocus}
                />
              )}

              <CarouselRow
                title={
                  isCurrentUser
                    ? 'Games Your Friends Are Playing'
                    : 'Games Your Mutual Friends Are Playing'
                }
                handleApiCall={async page => {
                  return await getPlayerProfileSectionGames(
                    isCurrentUser
                      ? PLAYER_PROFILE_SECTION.FRIEND_GAME_DESC
                      : PLAYER_PROFILE_SECTION.CONTINUE_PLAYING_MUTUAL_DESC,
                    profile?.user?.id,
                    page,
                    PAGE_LIMIT
                  );
                }}
                renderItem={(game, i) => (
                  <GameTile
                    key={game.name}
                    index={i}
                    name={game.name}
                    imageUrl={game.thumbnail ?? ''}
                    onEnterPress={() => navigate(`${gameDetailsRoute}/${game.slug}`)}
                    gameSlug={game?.slug}
                    esrbRating={game?.esrb_rating_img_url ?? ''}
                  />
                )}
                onFocus={onRowFocus}
              />
            </ProfileWrap>
          )}
        </MainWrap>
      )}
    </FocusContext.Provider>
  );
};
