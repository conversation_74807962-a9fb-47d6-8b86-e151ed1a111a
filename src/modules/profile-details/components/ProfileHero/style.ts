import styled from 'styled-components';
import { Col, Row } from 'styles/sharedStyles';

export const ProfileHeroWrap = styled.div<{ isCurrentUser?: boolean }>`
  background: ${props => props.theme?.surfaceSecondaryOpacity40};
  height: ${({ isCurrentUser }) => (isCurrentUser ? '330px' : '490px')};
  padding: 40px 48px 40px 32px;

  ${Row} {
    align-items: center;
  }
`;

export const ProfileImg = styled.img`
  width: 250px;
  height: 250px;
  object-fit: cover;
  outline: 6px solid ${props => props?.theme?.surfaceWhite};
  border-radius: 50%;
`;

export const IconWrap = styled(Col)`
  position: relative;
  height: 75px;
  width: 75px;
  border-radius: 50%;
  outline: 2px solid ${props => props?.theme?.surfaceWhite};
  align-items: center;
  justify-content: center;
  background: ${({ theme }) => theme?.surfaceSecondaryOpacity60};
  stroke-width: 2px;
  stroke: ${({ theme }) => theme?.iconWhite};
  overflow: visible;

  /* Create the outer glow */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    box-shadow: 0 0 12px 4px ${({ theme }) => theme?.iconWhite40};
    pointer-events: none; /* Prevent blocking interactions */
  }
`;
