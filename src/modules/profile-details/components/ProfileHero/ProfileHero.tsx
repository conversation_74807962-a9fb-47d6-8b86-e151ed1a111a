import { IFriendStatsRes, IProfileStatsRes } from 'types/api/user/friend.user.api.types';
import { IProfile } from 'types/api/user/profile.user.api.types';
import { IconWrap, ProfileHeroWrap, ProfileImg } from './style';
import { Col, OnlineDot, Row, VerticalLine } from 'styles/sharedStyles';
import { BodyXL, ButtonLG, DisplayLG, HeadingMD, LabelLG } from 'styles/theme/typography';
import { PrimaryButton } from 'shared/buttons';
import { AiFillMinusCircle } from 'react-icons/ai';
import { IoMdAddCircle } from 'react-icons/io';
import { FaUsers } from 'react-icons/fa';
import { FRIEND_STATUS } from 'enums/friend.enums';
import { sendFriendRequest, unFriendUser } from 'api/user-service/user-service.api';
import toast from 'react-hot-toast';
import ProfilePlaceholder from 'assets/icons/profile.svg';

interface IProfileHero {
  friendReq?: IFriendStatsRes;
  profile?: IProfile | null;
  profileStats?: IProfileStatsRes;
  isCurrentUser?: boolean;
  onFriendToggle?: () => void;
  isOnline?: boolean;
}

export const ProfileHero = (props: IProfileHero) => {
  const { friendReq, profile, profileStats, isCurrentUser, onFriendToggle, isOnline } = props;

  const renderFriendStatus = () => {
    if (friendReq?.status === FRIEND_STATUS.PENDING) {
      return 'Pending';
    } else if (friendReq?.status === FRIEND_STATUS.ACCEPTED) {
      return 'Unfriend';
    }
    return 'Add Friend';
  };

  const renderFriendIcon = () => {
    if (friendReq?.status === FRIEND_STATUS.PENDING) {
      return null;
    } else if (friendReq?.status === FRIEND_STATUS.ACCEPTED) {
      return <AiFillMinusCircle fontSize={48} />;
    }
    return <IoMdAddCircle fontSize={48} />;
  };

  const handleAddFriend = async () => {
    if (!profile?.user?.id) {
      return;
    }

    try {
      if (friendReq?.status === FRIEND_STATUS.PENDING) {
        toast.success(`Friend Request is pending`);
        return;
      } else if (friendReq?.status === FRIEND_STATUS.ACCEPTED) {
        await unFriendUser(profile?.user?.id);
        toast.success(`${profile?.user.display_name} is removed from your friends list`);
      } else {
        await sendFriendRequest(profile?.user?.id);
        toast.success('Friend request sent');
      }
    } catch (error) {
      console.log(error);
    } finally {
      onFriendToggle?.();
    }
  };

  return (
    <ProfileHeroWrap isCurrentUser={isCurrentUser}>
      <Row gap="32px">
        <ProfileImg
          src={profile?.user?.dp_url ?? ProfilePlaceholder}
          alt={profile?.user?.display_name}
        />
        <Col gap="32px">
          <Col gap="8px">
            <Row gap="24px">
              <DisplayLG>{profile?.user?.display_name}</DisplayLG>
              <Row>
                <OnlineDot isOnline={isOnline} size="16px" />
                <LabelLG>{isOnline ? 'Online' : 'Offline'}</LabelLG>
              </Row>
            </Row>
            <Row gap="16px">
              <BodyXL>Recently Played:</BodyXL>
              <HeadingMD>Forgotten Playland</HeadingMD>
            </Row>
          </Col>
          <Row gap="40px">
            <div>
              <ButtonLG>{profileStats?.friend_count ?? '0'}</ButtonLG>
              <BodyXL>Friends</BodyXL>
            </div>
            <VerticalLine height="55px" />
            <div>
              <ButtonLG>{profileStats?.user_followings ?? '0'}</ButtonLG>
              <BodyXL>Following</BodyXL>
            </div>
          </Row>
        </Col>
      </Row>

      {!isCurrentUser && (
        <Row gap="80px" marginTop="64px">
          <PrimaryButton
            icon={renderFriendIcon()}
            focusKey="add-friend-key"
            onClick={() => handleAddFriend()}
          >
            {renderFriendStatus()}
          </PrimaryButton>
          <Row gap="16px">
            <IconWrap>
              <FaUsers fontSize={45} />
            </IconWrap>
            <HeadingMD>{profileStats?.mutual_friend_count ?? '0'} Mutual Friends </HeadingMD>
          </Row>
        </Row>
      )}
    </ProfileHeroWrap>
  );
};
