import { GameTile } from 'shared/game-tile/GameTile';
import esrb from 'assets/esrb.svg';
import { Row } from './style';
import {
  filterPills,
  gameData,
  platformIcons,
  sampleCategoryData,
  sampleEventData,
  sampleImageData,
  sampleVideoData,
} from './data';
import { VideoTile } from 'shared/video-tile/VideoTile';
import { CategoryCard } from 'shared/category-card/CategoryCard';
import { PromoCard } from 'shared/promo-card/PromoCard';
import { ImageCard } from 'shared/image-card/ImageCard';
import { PillContainer } from 'shared/pill/PillContainer';
import { useState } from 'react';
import { IconGroup } from 'shared/IconGroup/IconGroup';
import { StarRating } from 'shared/star-rating/StarRating';
import { FriendList } from 'shared/friend-list';
import { KeyboardAlpNum } from 'shared/keyboard/keyboardAlphaNum/KeyboardAlpNum';

// A simple container for the tiles
const VideoRow = ({ children }) => (
  <div style={{ display: 'flex', gap: '24px', padding: '24px' }}>{children}</div>
);
const EventsRow = ({ children }) => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'row',
      gap: '32px',
      padding: '24px',
    }}
  >
    {children}
  </div>
);

export function Components() {
  const [currentFilter, setCurrentFilter] = useState('All');

  const handleFilterChange = selectedPill => {
    console.log(`Filter changed to: ${selectedPill}`);
    setCurrentFilter(selectedPill);
  };
  const onGameFocus = (layout, props, details) => {
    // Your focus logic here
    console.log('Focused on:', props.name);
  };

  const onGameEnterPress = (props, details) => {
    // Your enter press logic here
    alert(`You selected ${props.name}`);
  };
  const onVideoFocus = (layout, props) => {
    console.log(`Focused on video: ${props.title}`);
  };

  const onVideoEnterPress = props => {
    alert(`Playing: ${props.title}`);
  };

  const onCategoryFocus = (layout, props) => {
    console.log(`Focused on category: ${props.categoryName}`);
  };

  const onCategoryEnterPress = props => {
    alert(`Navigating to ${props.categoryName}`);
  };
  const onEventFocus = (layout, props) => {
    console.log(`Focused on event: ${props.imageUrl}`);
  };
  const onImageFocus = (layout, props) => {
    console.log(`Focused on image: ${props.altText}`);
  };

  const onImageEnterPress = props => {
    alert(`Opening image: ${props.altText}`);
  };
  return (
    <div style={{ background: '#141414', padding: '20px', overflow: 'auto' }}>
      <KeyboardAlpNum />

      <FriendList />
      {/* Render Vertical Cards */}
      <Row title="Top 10 This Week">
        {gameData.slice(0, 3).map((game, index) => (
          <GameTile
            key={game.name}
            index={index}
            name={game.name}
            imageUrl={game.imageUrl}
            onFocus={onGameFocus}
            onEnterPress={onGameEnterPress}
            isVertical={true} // Set prop to true
          />
        ))}
      </Row>

      {/* Render Horizontal Cards */}
      <Row title="Featured Games">
        {gameData.slice(3).map((game, index) => (
          <GameTile
            key={game.name}
            index={index}
            name={game.name}
            imageUrl={game.imageUrl}
            starRating={game.starRating}
            esrbRating={game.esrbRating}
            onFocus={onGameFocus}
            onEnterPress={onGameEnterPress}
            // isVertical is omitted, so it defaults to false
          />
        ))}
      </Row>
      <div>
        <h2 style={{ color: 'white', paddingLeft: '24px' }}>Latest Videos</h2>
        <VideoRow>
          {sampleVideoData.map(video => (
            <VideoTile
              key={video.title}
              {...video}
              videoUrl={video.videoUrl}
              onFocus={onVideoFocus}
              onEnterPress={onVideoEnterPress}
            />
          ))}
        </VideoRow>
      </div>
      <div>
        <h2 style={{ color: 'white', paddingLeft: '24px' }}>Browse by Genre</h2>
        <VideoRow>
          {sampleCategoryData.map(category => (
            <CategoryCard
              key={category.categoryName}
              {...category}
              onFocus={onCategoryFocus}
              onEnterPress={onCategoryEnterPress}
            />
          ))}
        </VideoRow>
      </div>
      <div>
        <h2 style={{ color: 'white', paddingLeft: '24px' }}>Upcoming Events</h2>
        <EventsRow>
          {sampleEventData.map((event, index) => (
            <PromoCard
              key={index}
              imageUrl={event.imageUrl}
              initialIsSaved={event.initialIsSaved}
              onFocus={onEventFocus}
            />
          ))}
        </EventsRow>
      </div>
      <div>
        <h2 style={{ color: 'white', paddingLeft: '24px' }}>Gallery</h2>
        <EventsRow>
          {sampleImageData.map(image => (
            <ImageCard
              key={image.altText}
              imageUrl={image.imageUrl}
              altText={image.altText}
              onFocus={onImageFocus}
              onEnterPress={onImageEnterPress}
            />
          ))}
        </EventsRow>
      </div>
      <div style={{ padding: '24px' }}>
        <PillContainer pills={filterPills} onPillSelect={handleFilterChange} />
        <div style={{ marginTop: '32px', color: 'white', fontSize: '24px' }}>
          <p>
            Currently showing content for: <strong>{currentFilter}</strong>
          </p>
        </div>
      </div>
      <div style={{ padding: '24px', backgroundColor: '#1a1a1a' }}>
        <h3 style={{ color: 'white', marginBottom: '16px' }}>Available Platforms</h3>
        {/* <IconGroup icons={platformIcons} /> */}
      </div>
      <StarRating rating={4} />
    </div>
  );
}
