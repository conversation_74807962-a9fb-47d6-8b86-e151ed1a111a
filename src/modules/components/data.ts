// URLs for ESRB rating images
const ESRB_RATINGS = {
  E: 'https://www.esrb.org/wp-content/uploads/2019/05/E.svg',
  T: 'https://www.esrb.org/wp-content/uploads/2019/05/T.svg',
  M: 'https://www.esrb.org/wp-content/uploads/2019/05/M.svg',
};

export const gameData = [
  // Data for Vertical Cards (Top 3)
  {
    name: 'Hades',
    imageUrl: 'https://i.imgur.com/2sO3V3S.png', // Using a direct link for the example
  },
  {
    name: 'Cyberpunk 2077',
    imageUrl: 'https://picsum.photos/264/330?random=2',
  },
  {
    name: 'Elden Ring',
    imageUrl: 'https://picsum.photos/264/330?random=3',
  },
  // Data for Horizontal Cards
  {
    name: 'Brawl Stars',
    imageUrl: 'https://i.imgur.com/GZ5Zf2F.png', // Using a direct link for the example
    starRating: 4.5,
    esrbRating: ESRB_RATINGS.E,
  },
  {
    name: 'Cosmic Odyssey',
    imageUrl: 'https://picsum.photos/428/240?random=4',
    starRating: 4.8,
    esrbRating: ESRB_RATINGS.T,
  },
  {
    name: 'Shadowfall',
    imageUrl: 'https://picsum.photos/428/240?random=5',
    starRating: 5.0,
    esrbRating: ESRB_RATINGS.M,
  },
];
export const sampleVideoData = [
  {
    title: 'New Gameplay Features Revealed for Starfield',
    thumbnailUrl: 'https://i.imgur.com/k2fXFVm.png',
    // A sample video from a free stock source
    videoUrl: 'http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    channelName: 'Tencent Games',
    channelLogo: '10',
    timeAgo: '2 Hrs Ago',
    duration: '10:15',
    isVerified: true,
  },
  {
    title: 'Cyberpunk 2077: Phantom Liberty Full Review',
    thumbnailUrl: 'https://picsum.photos/412/232?random=1',
    // This item has no video, so it will only show the thumbnail
    videoUrl: undefined,
    channelName: 'IGN',
    channelLogo: 'I',
    timeAgo: '8 Hrs Ago',
    duration: '22:45',
    isVerified: true,
  },
  {
    title: 'The Future of Open World Games',
    thumbnailUrl: 'https://picsum.photos/412/232?random=2',
    // Another sample video
    videoUrl:
      'https://assets.mixkit.co/videos/preview/mixkit-futuristic-long-highway-with-fast-moving-cars-39824-large.mp4',
    channelName: 'GameSpot',
    channelLogo: 'GS',
    timeAgo: '1 Day Ago',
    duration: '15:30',
    isVerified: false,
  },
];
export const sampleCategoryData = [
  {
    categoryName: 'Adventure',
    imageUrl: 'https://i.imgur.com/gSNeO1y.png', // Using direct link for the example
  },
  {
    categoryName: 'Action',
    imageUrl: 'https://picsum.photos/323/105?random=1',
  },
  {
    categoryName: 'RPG',
    imageUrl: 'https://picsum.photos/323/105?random=2',
  },
  {
    categoryName: 'Strategy',
    imageUrl: 'https://picsum.photos/323/105?random=3',
  },
];
export const sampleEventData = [
  {
    imageUrl: 'https://i.imgur.com/vHqL3ng.jpg', // Using direct link for the example
    initialIsSaved: true,
  },
  {
    imageUrl: 'https://picsum.photos/544/216?random=1',
    initialIsSaved: false,
  },
];
export const sampleImageData = [
  {
    altText: 'Super Mario gameplay scene',
    imageUrl: 'https://i.imgur.com/u7SO32K.png', // Direct link to the example image
  },
  {
    altText: 'Fantasy landscape with a castle',
    imageUrl: 'https://picsum.photos/412/232?random=1',
  },
  {
    altText: 'Cyberpunk city street at night',
    imageUrl: 'https://picsum.photos/412/232?random=2',
  },
];
export const filterPills = ['All', 'Games', 'Trailers', 'Publishers', 'Users', 'Other Content'];
// Example using URLs to publicly available icons that resemble the design.
export const platformIcons = [
  {
    src: 'https://www.svgrepo.com/show/509201/windows.svg',
    alt: 'Windows PC',
  },
  {
    src: 'https://www.svgrepo.com/show/447813/mobile.svg',
    alt: 'Mobile',
  },
  {
    src: 'https://www.svgrepo.com/show/443431/cloud.svg',
    alt: 'Cloud Gaming',
  },
];
