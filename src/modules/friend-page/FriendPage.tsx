import React, { useEffect, useState } from 'react';
import { FriendPageWrap } from './style';
import { KeyboardAlpNum } from 'shared/keyboard/keyboardAlphaNum';
import { Col, VerticalLine } from 'styles/sharedStyles';
import { FocusContext, useFocusable } from '@noriginmedia/norigin-spatial-navigation';
import { SearchBar } from 'shared/search-bar';
import { FriendList } from 'shared/friend-list';
import { PrimaryButton } from 'shared/buttons';
import { FaArrowLeft } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

export const FriendPage = () => {
  const [searchText, setSearchText] = useState<string>();
  const [searchVal, setSearchVal] = useState<string>();
  const navigate = useNavigate();

  const { focusKey, focusSelf } = useFocusable({
    focusable: true,
    autoRestoreFocus: true,
    trackChildren: true,
    preferredChildFocusKey: 'keyboard-key-a',
  });

  useEffect(() => {
    const timer = setTimeout(() => {
      focusSelf();
    }, 300);

    return () => clearTimeout(timer);
  }, [focusSelf]);

  return (
    <FocusContext.Provider value={focusKey}>
      <FriendPageWrap>
        <Col maxWidth="708px" justify="space-between" padding="0px 0px 50px 0px">
          <Col gap="40px">
            <SearchBar searchText={searchText} variant="standard" isFocusable={false} />
            <KeyboardAlpNum
              alpHeight="358px"
              onSearch={() => {
                setSearchVal(searchText);
              }}
              onChange={val => setSearchText(val)}
            />
          </Col>
          <PrimaryButton onClick={() => navigate(-1)} icon={<FaArrowLeft fontSize={42} />}>
            Go back
          </PrimaryButton>
        </Col>
        <Col padding="0px 90px">
          <VerticalLine height="100%" />
        </Col>
        <Col width="100%">
          <FriendList searchText={searchVal} />
        </Col>
      </FriendPageWrap>
    </FocusContext.Provider>
  );
};
