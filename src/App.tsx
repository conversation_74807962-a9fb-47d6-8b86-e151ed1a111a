import { init } from '@noriginmedia/norigin-spatial-navigation';
import { useRemoteControl } from 'hooks/useRemoteControl';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Routing from 'routes/Routing';
import { Layout } from 'shared/layout';
import { ThemeContextProvider } from 'state-management/context/ThemeContext';
import './App.css';
import { IdleScreenContextProvider } from 'state-management/context/IdleScreenContext';
import { loginUser } from 'api/user-service/user-service.api';
import { useAppDispatch } from 'hooks/redux.hooks';
import { getProfileDetails } from 'state-management/redux/actions/profile.action';
import { homePath } from 'routes/path';
import { ModalProvider } from 'state-management/context/ModalContext';

init({
  debug: false,
  visualDebug: false,
  distanceCalculationMethod: 'center',
});

const App: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { pathname } = useLocation();

  function handleExitPrompt(): void {
    const confirmed = window.confirm('Are you sure you want to exit?');
    if (confirmed) {
      try {
        const app = tizen.application.getCurrentApplication();
        app.exit();
      } catch (error) {
        console.error('Error closing the app:', error);
      }
    }
  }

  useRemoteControl({
    onBack: () => {
      // prevent back press on modal open
      if (document.getElementById('active-modal-container') !== null) {
        return;
      }

      if (pathname === homePath) {
        handleExitPrompt();
        return;
      }

      navigate(-1);
    },
  });

  const [currentPage, setCurrentPage] = useState('home');

  useEffect(() => {
    // !IMPORTANT ONLY FOR DEV PURPOSE DON'T REMOVE
    loginUser({ email: '<EMAIL>', password: 'Rapid@123' }).then(() => {
      // loginUser({ email: '<EMAIL>', password: 'Rapid@123' }).then(() => {
      // loginUser({ email: '<EMAIL>', password: 'Rapid@1234' }).then(() => {
      dispatch(getProfileDetails());
    });
  }, [dispatch]);

  const handleNavigate = (page: string, path: string) => {
    setCurrentPage(page);
    navigate(path);
  };

  return (
    <div id="main-app" data-testid="main-app">
      <ThemeContextProvider>
        <IdleScreenContextProvider>
          <ModalProvider>
            <Layout currentPage={currentPage} onNavigate={handleNavigate}>
              <Routing />
            </Layout>
          </ModalProvider>
        </IdleScreenContextProvider>
      </ThemeContextProvider>
    </div>
  );
};

export default App;
