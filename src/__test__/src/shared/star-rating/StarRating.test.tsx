import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import { screen } from '@testing-library/dom';
import { IStarRatingProps, StarRating } from 'shared/star-rating/StarRating';

// Mock the styled components
vi.mock('shared/star-rating/style', () => ({
  StarContainer: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div className={className} data-testid="star-container">
      {children}
    </div>
  ),
  StarIcon: ({ children, style }: { children: React.ReactNode; style?: React.CSSProperties }) => (
    <span style={style} data-testid="star-icon">
      {children}
    </span>
  ),
  HalfStarIcon: ({ children }: { children: React.ReactNode }) => (
    <span data-testid="half-star-icon">{children}</span>
  ),
}));

describe('StarRating Component', () => {
  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<StarRating rating={3} />);
      expect(screen.getByTestId('star-container')).toBeInTheDocument();
    });

    it('applies custom className when provided', () => {
      render(<StarRating rating={3} className="custom-star-rating" />);
      expect(screen.getByTestId('star-container')).toHaveClass('custom-star-rating');
    });
  });

  describe('Full Stars Rendering', () => {
    it('renders correct number of full stars for whole number rating', () => {
      render(<StarRating rating={3} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      expect(fullStars).toHaveLength(3);
    });

    it('renders 5 full stars for maximum rating', () => {
      render(<StarRating rating={5} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      expect(fullStars).toHaveLength(5);
    });

    it('renders 0 full stars for zero rating', () => {
      render(<StarRating rating={0} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      expect(fullStars).toHaveLength(0);
    });
  });

  describe('Half Stars Rendering', () => {
    it('renders half star for 0.5 decimal rating', () => {
      render(<StarRating rating={3.5} />);
      const halfStars = screen.getAllByTestId('half-star-icon');
      expect(halfStars).toHaveLength(1);
      expect(halfStars[0]).toHaveTextContent('★');
    });

    it('renders outline star for non-0.5 decimal rating', () => {
      render(<StarRating rating={3.3} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const outlineStars = starIcons.filter(
        star => star.textContent === '☆' && !star.style.opacity
      );
      expect(outlineStars).toHaveLength(1);
    });

    it('renders outline star for 0.7 decimal rating', () => {
      render(<StarRating rating={2.7} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const outlineStars = starIcons.filter(
        star => star.textContent === '☆' && !star.style.opacity
      );
      expect(outlineStars).toHaveLength(1);
    });
  });

  describe('Empty Stars Rendering', () => {
    it('renders correct number of empty stars', () => {
      render(<StarRating rating={2} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const emptyStars = starIcons.filter(
        star => star.textContent === '☆' && star.style.opacity === '0.3'
      );
      expect(emptyStars).toHaveLength(3);
    });

    it('renders no empty stars for maximum rating', () => {
      render(<StarRating rating={5} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const emptyStars = starIcons.filter(
        star => star.textContent === '☆' && star.style.opacity === '0.3'
      );
      expect(emptyStars).toHaveLength(0);
    });

    it('renders all empty stars for zero rating', () => {
      render(<StarRating rating={0} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const emptyStars = starIcons.filter(
        star => star.textContent === '☆' && star.style.opacity === '0.3'
      );
      expect(emptyStars).toHaveLength(5);
    });
  });

  describe('Custom maxStars Prop', () => {
    it('renders correct total stars when maxStars is 3', () => {
      render(<StarRating rating={2} maxStars={3} />);
      const allStars = screen.getAllByTestId('star-icon');
      expect(allStars).toHaveLength(3);
    });

    it('renders correct total stars when maxStars is 10', () => {
      render(<StarRating rating={7} maxStars={10} />);
      const allStars = screen.getAllByTestId('star-icon');
      expect(allStars).toHaveLength(10);
    });

    it('handles rating higher than maxStars correctly', () => {
      render(<StarRating rating={8} maxStars={5} />);
      const starIcons = screen.getAllByTestId('star-icon');
      // The component renders 8 full stars even though maxStars is 5
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      expect(fullStars).toHaveLength(8);
      // But total stars should still be based on the rating logic
      expect(starIcons).toHaveLength(8); // 8 full stars, no empty stars since rating >= maxStars
    });
  });

  describe('Edge Cases', () => {
    it('handles negative rating', () => {
      render(<StarRating rating={-1} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const emptyStars = starIcons.filter(
        star => star.textContent === '☆' && star.style.opacity === '0.3'
      );
      // For negative rating, we actually get 6 empty stars (not 5)
      expect(emptyStars).toHaveLength(6);
      expect(starIcons).toHaveLength(6); // All empty stars
    });

    it('handles very high rating', () => {
      render(<StarRating rating={100} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      expect(fullStars).toHaveLength(100); // Component renders 100 full stars
    });

    it('handles decimal rating close to 1', () => {
      render(<StarRating rating={0.9} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const outlineStars = starIcons.filter(
        star => star.textContent === '☆' && !star.style.opacity
      );
      expect(outlineStars).toHaveLength(1);
    });
  });

  describe('Star Distribution Logic', () => {
    it('correctly distributes stars for 3.5 rating', () => {
      render(<StarRating rating={3.5} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const halfStars = screen.getAllByTestId('half-star-icon');
      expect(starIcons.length + halfStars.length).toBe(5); // 3 full + 1 half + 1 empty
    });

    it('correctly distributes stars for 2.3 rating', () => {
      render(<StarRating rating={2.3} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const fullStars = starIcons.filter(star => star.textContent === '★' && !star.style.opacity);
      const outlineStars = starIcons.filter(
        star => star.textContent === '☆' && !star.style.opacity
      );
      const emptyStars = starIcons.filter(
        star => star.textContent === '☆' && star.style.opacity === '0.3'
      );

      expect(fullStars).toHaveLength(2); // 2 full stars
      expect(outlineStars).toHaveLength(1); // 1 outline star for 0.3 decimal
      expect(emptyStars).toHaveLength(2); // 2 empty stars
    });

    it('correctly distributes stars for 4.5 rating with maxStars 6', () => {
      render(<StarRating rating={4.5} maxStars={6} />);
      const starIcons = screen.getAllByTestId('star-icon');
      const halfStars = screen.getAllByTestId('half-star-icon');
      expect(starIcons.length + halfStars.length).toBe(6); // 4 full + 1 half + 1 empty
    });
  });

  describe('Component Props Interface', () => {
    it('accepts all required props', () => {
      const props: IStarRatingProps = {
        rating: 4.5,
        maxStars: 5,
        className: 'test-class',
      };
      render(<StarRating {...props} />);
      expect(screen.getByTestId('star-container')).toBeInTheDocument();
    });

    it('works with only required rating prop', () => {
      render(<StarRating rating={3} />);
      expect(screen.getByTestId('star-container')).toBeInTheDocument();
    });
  });
});
