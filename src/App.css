#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* App.css */
.grid {
  display: flex;
  gap: 20px;
  padding: 40px;
}

button {
  padding: 20px 40px;
  font-size: 18px;
  border: none;
  background: #333;
  color: white;
  border-radius: 10px;
  transition: transform 0.2s;
}

button.focused {
  outline: 3px solid #00ffcc;
  transform: scale(1.1);
}

.menu-item {
  padding: 15px 20px;
  margin: 5px 0;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.menu-item.focused,
.menu-item.selected {
  border-color: #0078d4;
  background-color: rgba(0, 120, 212, 0.1);
  transform: scale(1.05);
}
