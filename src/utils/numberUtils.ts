/**
 * Formats a number with appropriate suffix (k for thousands, m for millions)
 *
 * @param num - The number to format
 * @returns The formatted number as a string with suffix, or the original number if less than 1000
 *
 * @example
 * formatNumberWithSuffix(1500) // returns "1.5K"
 * formatNumberWithSuffix(2500000) // returns "2.5M"
 * formatNumberWithSuffix(500) // returns 500
 */
export const formatNumberWithSuffix = (num: number) => {
  const THOUSAND = 1000;
  const MILLION = 1000000;

  if (Math.abs(num) >= MILLION) {
    return (num / MILLION).toFixed(1) + 'M';
  }
  if (Math.abs(num) >= THOUSAND) {
    return (num / THOUSAND).toFixed(1) + 'K';
  }
  return num;
};
