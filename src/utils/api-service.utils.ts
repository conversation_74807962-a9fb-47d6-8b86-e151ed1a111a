import axios, { AxiosResponse } from 'axios';
import SecureLS from 'secure-ls';
import { storage } from './storage.utils';
import { STORAGE } from 'enums/storage.enums';

export class ApiService {
  private readonly baseUrl: string;
  private readonly storageService: SecureLS;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    this.storageService = storage;
  }

  private async getHeaders(
    options: {
      isAuth?: boolean;
      additionalHeaders?: Record<string, string | number | undefined>;
    } = {}
  ): Promise<Record<string, string>> {
    const { isAuth = true, additionalHeaders } = options;

    // Clean undefined values from additionalHeaders
    const filteredHeaders: Record<string, string | number> = {};
    if (additionalHeaders) {
      Object.entries(additionalHeaders).forEach(([key, value]) => {
        if (value !== undefined) {
          filteredHeaders[key] = value;
        }
      });
    }

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...filteredHeaders,
    };

    // Attach token if isAuth is true
    if (isAuth) {
      const token = this.storageService.get(STORAGE.AUTH_TOKEN); // Use consistent key
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  private getUrl(endpoint: string): string {
    const normalizedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
    return `${this.baseUrl}${normalizedEndpoint}`;
  }

  async get<T = any>(
    endpoint: string,
    params?: Record<string, string | number | undefined>, // <-- Treat as query params
    options: { isAuth?: boolean } = {}
  ): Promise<AxiosResponse<T>> {
    const headers = await this.getHeaders({ isAuth: options.isAuth });

    return axios.get(this.getUrl(endpoint), {
      headers,
      params,
    });
  }

  async post<T = any>(
    endpoint: string,
    data?: any,
    options: {
      isAuth?: boolean;
      additionalHeaders?: Record<string, string | undefined>;
    } = {}
  ): Promise<AxiosResponse<T>> {
    const headers = await this.getHeaders(options);
    return axios.post(this.getUrl(endpoint), data, { headers });
  }

  async put<T = any>(
    endpoint: string,
    data?: any,
    options: {
      isAuth?: boolean;
      additionalHeaders?: Record<string, string | undefined>;
    } = {}
  ): Promise<AxiosResponse<T>> {
    const headers = await this.getHeaders(options);
    return axios.put(this.getUrl(endpoint), data, { headers });
  }

  async patch<T = any>(
    endpoint: string,
    data?: any,
    options: {
      isAuth?: boolean;
      additionalHeaders?: Record<string, string | undefined>;
    } = {}
  ): Promise<AxiosResponse<T>> {
    const headers = await this.getHeaders(options);
    return axios.patch(this.getUrl(endpoint), data, { headers });
  }

  async delete<T = any>(
    endpoint: string,
    data?: Record<string, any>,
    options: {
      isAuth?: boolean;
      params?: Record<string, string | number | undefined>; // <-- Keep query params optional
    } = {}
  ): Promise<AxiosResponse<T>> {
    const headers = await this.getHeaders(options);
    return axios.delete(this.getUrl(endpoint), {
      headers,
      params: options.params,
      data, // <-- Send body explicitly
    });
  }

  public getAuthToken(): string | null {
    return this.storageService.get(STORAGE.AUTH_TOKEN);
  }
}
