import moment from 'moment-timezone';

/**
 * Formats a datetime string from ISO format to MM/DD/YY format
 * @param dateTimeString - ISO datetime string (e.g., "2025-05-21T06:30:00")
 * @param timezone - Optional timezone (defaults to user's local timezone)
 * @returns Formatted date string (e.g., "5/21/25")
 */
export const formatDateTime = (dateTimeString: string, timezone?: string): string => {
  if (!dateTimeString) {
    return '';
  }

  try {
    const momentDate = timezone ? moment.tz(dateTimeString, timezone) : moment(dateTimeString);

    // Format as M/D/YY (e.g., 5/21/25)
    return momentDate.format('M/D/YY');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateTimeString; // Return original string if formatting fails
  }
};

/**
 * Formats a datetime string to a more readable format
 * @param dateTimeString - ISO datetime string
 * @param timezone - Optional timezone
 * @returns Formatted date string (e.g., "May 21, 2025")
 */
export const formatDateTimeReadable = (dateTimeString: string, timezone?: string): string => {
  if (!dateTimeString) {
    return '';
  }

  try {
    const momentDate = timezone ? moment.tz(dateTimeString, timezone) : moment(dateTimeString);

    return momentDate.format('MMM D, YYYY');
  } catch (error) {
    console.error('Error formatting readable date:', error);
    return dateTimeString;
  }
};

/**
 * Checks if a date is in the past
 * @param dateTimeString - ISO datetime string
 * @param timezone - Optional timezone
 * @returns Boolean indicating if the date is in the past
 */
export const isDateInPast = (dateTimeString: string, timezone?: string): boolean => {
  if (!dateTimeString) {
    return false;
  }

  try {
    const momentDate = timezone ? moment.tz(dateTimeString, timezone) : moment(dateTimeString);

    return momentDate.isBefore(moment());
  } catch (error) {
    console.error('Error checking if date is in past:', error);
    return false;
  }
};

/**
 * Gets the time remaining until a date
 * @param dateTimeString - ISO datetime string
 * @param timezone - Optional timezone
 * @returns Human readable time remaining (e.g., "2 days", "3 hours")
 */
export const getTimeRemaining = (dateTimeString: string, timezone?: string): string => {
  if (!dateTimeString) {
    return '';
  }

  try {
    const momentDate = timezone ? moment.tz(dateTimeString, timezone) : moment(dateTimeString);

    return momentDate.fromNow();
  } catch (error) {
    console.error('Error getting time remaining:', error);
    return '';
  }
};
