import placeholder from 'assets/icons/placeholder.svg';
export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  const minute = 60;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30; // Approximation
  const year = day * 365; // Approximation

  if (seconds < minute) {
    return 'Just now';
  } else if (seconds < hour) {
    const minutes = Math.floor(seconds / minute);
    return `${minutes} min${minutes > 1 ? 's' : ''} ago`;
  } else if (seconds < day) {
    const hours = Math.floor(seconds / hour);
    return `${hours} hr${hours > 1 ? 's' : ''} ago`;
  } else if (seconds < month) {
    const days = Math.floor(seconds / day);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else if (seconds < year) {
    const months = Math.floor(seconds / month);
    return `${months} month${months > 1 ? 's' : ''} ago`;
  } else {
    const years = Math.floor(seconds / year);
    return `${years} year${years > 1 ? 's' : ''} ago`;
  }
};
export function formatDuration(totalSeconds: number, useWordsFormat: boolean = false): string {
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  if (useWordsFormat) {
    // Return format like "8 min 6 sec"
    const minText = minutes > 0 ? `${minutes} min` : '';
    const secText = seconds > 0 ? `${seconds} sec` : '';

    if (minutes > 0 && seconds > 0) {
      return `${minText} ${secText}`;
    } else if (minutes > 0) {
      return minText;
    } else {
      return secText || '0 sec';
    }
  }

  // Default format: "08:06"
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');

  return `${formattedMinutes}:${formattedSeconds}`;
}
export const handleLogoError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
  e.currentTarget.onerror = null; // Prevent infinite loops
  e.currentTarget.src = placeholder;
};
export function formatUnixTimestamp(timestamp: number): string {
  // Return an empty string or handle invalid input
  if (!timestamp || typeof timestamp !== 'number') {
    return '';
  }

  // A 10-digit timestamp covers dates up to the year 2286.
  // If the number has more than 10 digits, we can safely assume it's in milliseconds.
  const isMilliseconds = String(timestamp).length > 10;

  // If it's not milliseconds, multiply by 1000. Otherwise, use it as is.
  const date = new Date(isMilliseconds ? timestamp : timestamp * 1000);

  // Extract the month, day, and year
  const month = date.getMonth() + 1; // getMonth() is 0-indexed
  const day = date.getDate();
  const year = date.getFullYear();

  // Return the formatted string
  return `${month}/${day}/${year}`;
}
