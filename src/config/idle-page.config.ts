import { PAGE_ID } from 'enums/idle-page.enums';
import {
  addProfilePath,
  gameDetailsRoute,
  gameInterstitialRoute,
  gamePlayRoute,
  homePath,
  libraryPagePath,
  profileRoute,
  profileSwitchPath,
  publisherRoute,
  searchPagePath,
  signInPath,
  signUpPath,
  videoPageRoute,
} from 'routes/path';

const idlePageConfig = {
  [homePath]: PAGE_ID.HOME_PAGE,
  [gameDetailsRoute]: PAGE_ID.GAME_PAGE,
  [profileRoute]: PAGE_ID.PROFILE_PAGE,
  [gameInterstitialRoute]: PAGE_ID.GAME_INTERSTITIAL,
  [libraryPagePath]: PAGE_ID.LIBRARY_PAGE,
  [publisherRoute]: PAGE_ID.PUBLISHER_PAGE,
  [searchPagePath]: PAGE_ID.SEARCH_PAGE,
  [videoPageRoute]: PAGE_ID.VIDEO_PLAYER,
  [gamePlayRoute]: PAGE_ID.GAME_PLAYING,
  [profileSwitchPath]: PAGE_ID.PROFILE_SWITCH,
  [addProfilePath]: PAGE_ID.ADD_PROFILE,
  [signUpPath]: PAGE_ID.SIGN_UP,
  [signInPath]: PAGE_ID.SIGN_IN,
};

export default idlePageConfig;
