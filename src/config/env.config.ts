// Environment configuration for Vite React app
const appEnv = {
  userBaseUrl: import.meta.env.VITE_USER_BASE_URL ?? '',
  adminBaseUrl: import.meta.env.VITE_ADMIN_BASE_URL ?? '',
  gameBaseUrl: import.meta.env.VITE_GAME_BASE_URL ?? '',
  marketplaceBaseUrl: import.meta.env.VITE_MARKETPLACE_BASE_URL ?? '',
  launchpadBaseUrl: import.meta.env.VITE_LAUNCHPAD_BASE_URL ?? '',
  questBaseUrl: import.meta.env.VITE_QUEST_BASE_URL ?? '',
  questWebSocketBaseUrl: import.meta.env.VITE_QUEST_WEB_SOCKET_BASE_URL ?? '',
  subscriptionBaseUrl: import.meta.env.VITE_SUBSCRIPTION_BASE_URL ?? '',
  userSseBaseUrl: import.meta.env.VITE_USER_SSE_BASE_URL ?? '',
  webCarouselBaseUrl: import.meta.env.VITE_WEB_CAROUSEL_BASE_URL ?? '',
  userWebSocketBaseUrl: import.meta.env.VITE_USER_WEBSOCKET_BASE_URL ?? '',
  gameWebSocketBaseUrl: import.meta.env.VITE_GAME_WEBSOCKET_BASE_URL ?? '',
  algoliaAppId: import.meta.env.VITE_ALGOLIA_APPLICATION_ID ?? '',
  algoliaApiKey: import.meta.env.VITE_ALGOLIA_API_KEY ?? '',
  appOrigin: import.meta.env.VITE_APP_ORIGIN ?? '',
};

export default appEnv;
