import { createGlobalStyle } from 'styled-components';
import { fontSizes } from './fonts';

export const GlobalStyle = createGlobalStyle`
  #root, #main-app {  
    transition: margin 300ms ease-in-out;        
    background: ${props => props.theme?.bgColor};;
    overflow-y:scroll !important;
    overflow-x: hidden;
    width: 100%;
    max-width:1920px;
    height: 100%;
    margin: 0px;
    padding: 0px;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    display: flex;
    flex-flow: column;
    max-width: 100%;
  }

  h1, h2, h3, h4 {
    margin: 0;
  }
  h1 { font-size: ${fontSizes.mediaXXL}; }
  h2 { font-size: ${fontSizes.mediaXL}; }
  h3 { font-size: ${fontSizes.mediaL}; }
  h4 { font-size: ${fontSizes.mediaM}; }

  html {
    box-sizing: border-box;
    -moz-osx-font-smoothing: grayscale;
    background-color: ${props => props.theme?.bgColor};
  }

  html, body {
    font-size: 16px;
    height: 100%;
    width: 100%;
    overflow-x: hidden;
  }

  body {
    min-height: 100%;
    margin: 0;
    padding: 0;
    background-color: ${props => props.theme?.bgColor};
  }

  /* Hide scrollbar but allow scrolling */
  * {
    scrollbar-width: none; /* Firefox */
  }
  *::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-transition: color 9999s ease-out, background-color 9999s ease-out;
    -webkit-transition-delay: 9999s;
  }

  *, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  a {
    text-decoration: none;
  }

  label {
    font-size: ${fontSizes.mediaXS};
    position: relative;
  }

  dialog{
    all: unset;
    display: unset;
  }
`;
