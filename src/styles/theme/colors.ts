import { IColors } from 'types/theme.type';

export const AppColors: IColors = {
  // Brand Tokens
  darkNavy: '#1B1D26', // Primary (Dark Navy)
  grey: '#313544', // Secondary (Grey)
  periwinkle: '#8080FF', // Tertiary (Accent/Periwinkle)
  phyndWhite: '#F5F5F5', // Phynd White

  gold: '#e3ba24',
  green: '#5ed36a',
  red: '#E91E63',
  iconWhite: '#e0e0e0',
  offWhite: '#D9D9D9',
  perlWhite: '#F4F3FC',
  midnightSlate: '#454B60',
} as const;

// Type for color keys
export type AppColorKey = keyof typeof AppColors;
