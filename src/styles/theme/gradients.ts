import { IGradients } from 'types/theme.type';
import { applyOpacity } from 'utils/styleUtils';
import { AppColors } from './colors';

export const gradients: IGradients = {
  gameTileOverlayGradient: `linear-gradient(180deg, ${applyOpacity(AppColors.darkNavy, 0)} 0%, ${applyOpacity(AppColors.darkNavy, 0.9)} 100%)`,
  idleOverlayGradient: `linear-gradient(180deg, ${applyOpacity(AppColors.darkNavy, 0)} 50%, ${applyOpacity(AppColors.darkNavy, 0.9)} 101.93%)`,
  interstitialOverlayGradient: `linear-gradient(
    270deg,
    ${applyOpacity(AppColors.darkNavy, 0)} 36.13%,
    ${applyOpacity(AppColors.darkNavy, 0.72)} 59.62%,
    ${applyOpacity(AppColors.darkNavy, 0.95)} 83.27%
  )`,
};
