import { ITheme } from 'types/theme.type';

import { AppColors } from './colors';
import { applyOpacity } from 'utils/styleUtils';
import { gradients } from './gradients';

export const darkTheme: ITheme = {
  ...gradients,
  // Surface tokens
  bgColor: AppColors.darkNavy,
  surfaceSecondaryOpacity40: applyOpacity(AppColors.grey, 0.4), // 40% opacity
  surfaceSecondaryOpacity60: applyOpacity(AppColors.grey, 0.6), // 60% opacity
  bgColorOpacity90: applyOpacity(AppColors.darkNavy, 0.9), // 60% opacity
  surfaceSecondaryOpacity100: AppColors.grey, // 100% opacity
  surfaceWhite: AppColors.phyndWhite, // 100% opacity
  offWhite: AppColors.offWhite,
  perlWhite: AppColors.perlWhite,

  // Typography tokens
  textPrimary: AppColors.phyndWhite,
  textSecondary: applyOpacity(AppColors.phyndWhite, 0.7),
  textInvert: AppColors.darkNavy,

  // Outline tokens
  outlineStrokeFocusWhite: AppColors.phyndWhite,
  outlineStrokeFocusTertiary: AppColors.periwinkle,
  outlineStrokeStandardWhite: AppColors.phyndWhite,

  // Icon tokens
  iconGold: AppColors.gold,
  iconInverted: AppColors.darkNavy,
  favorite: AppColors.red,
  iconWhite: AppColors.iconWhite,
  iconWhite40: applyOpacity(AppColors.iconWhite, 0.4),

  // Status tokens
  statusOnline: AppColors.green,

  // button colors
  btnSecondary: AppColors.midnightSlate,
};

export const getTheme = () => darkTheme;
