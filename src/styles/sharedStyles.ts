import styled, { css, keyframes } from 'styled-components';
import { IColProps, ILoaderProps, IVerticalLine } from 'types/theme.type';

export const activeStyle = css`
  background: ${props => props?.theme?.surfaceWhite};
  color: ${props => props?.theme?.bgColor};
`;

export const defaultStyle = css`
  background: ${props => props?.theme?.bgColor};
  color: ${props => props?.theme?.surfaceWhite};
`;

export const Col = styled.div.withConfig({
  shouldForwardProp: prop =>
    ![
      'gap',
      'maxWidth',
      'align',
      'justify',
      'textAlign',
      'margin',
      'marginLeft',
      'marginRight',
      'marginTop',
      'marginBottom',
      'padding',
      'width',
      'height',
      'order',
      'self',
      'overflow',
      'flex',
    ].includes(prop),
})<IColProps>`
  display: flex;
  flex-direction: column;
  gap: ${(props: IColProps) => props.gap || '6px'};
  max-width: ${(props: IColProps) => props.maxWidth};
  align-items: ${(props: IColProps) => props.align};
  justify-content: ${(props: IColProps) => props.justify};
  text-align: ${(props: IColProps) => props.textAlign};
  margin: ${(props: IColProps) => props.margin};
  margin-left: ${(props: IColProps) => props.marginLeft};
  margin-right: ${(props: IColProps) => props.marginRight};
  margin-top: ${(props: IColProps) => props.marginTop};
  margin-bottom: ${(props: IColProps) => props.marginBottom};
  padding: ${(props: IColProps) => props.padding};
  width: ${props => props.width};
  height: ${(props: IColProps) => props.height};
  order: ${props => props.order};
  align-self: ${props => props.self};
  overflow: ${props => props.overflow};
  flex: ${props => props.flex};
`;

export const Row = styled.div.withConfig({
  shouldForwardProp: prop =>
    ![
      'gap',
      'align',
      'justify',
      'width',
      'height',
      'margin',
      'marginLeft',
      'marginRight',
      'marginTop',
      'marginBottom',
      'wrap',
      'padding',
      'maxWidth',
      'minWidth',
      'overflow',
      'flex',
      'flexWrap',
      'flexDirection',
    ].includes(prop),
})<IColProps>`
  display: flex;
  gap: ${(props: IColProps) => props.gap || '10px'};
  align-items: ${(props: IColProps) => props.align};
  justify-content: ${(props: IColProps) => props.justify};
  width: ${(props: IColProps) => props.width};
  height: ${(props: IColProps) => props.height};
  margin: ${(props: IColProps) => props.margin};
  margin-left: ${(props: IColProps) => props.marginLeft};
  margin-right: ${(props: IColProps) => props.marginRight};
  margin-top: ${(props: IColProps) => props.marginTop};
  margin-bottom: ${(props: IColProps) => props.marginBottom};
  flex-wrap: ${(props: IColProps) => props.wrap};
  padding: ${(props: IColProps) => props.padding};
  max-width: ${(props: IColProps) => props.maxWidth};
  min-width: ${(props: IColProps) => props.minWidth};
  overflow: ${props => props.overflow};
  flex: ${props => props.flex};
  flex-wrap: ${props => props.flexWrap};
  flex-direction: ${props => (props.flexDirection ? 'column' : 'row')};
`;

export const VerticalLine = styled.div<IVerticalLine>`
  height: ${props => props?.height ?? '38px'};
  border-right: 2px solid ${props => props?.color ?? props.theme.surfaceWhite};
`;

export const applyOpacity = (hexColor: string, opacity: number): string => {
  const hex = hexColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

export const CircularLoader = styled.div<ILoaderProps>`
  ${({ size = 40, color, theme }) => css`
    width: ${size}px;
    height: ${size}px;
    border: ${size / 8}px solid ${theme?.surfaceSecondaryOpacity40};
    border-top-color: ${color ?? theme?.outlineStrokeFocusTertiary};
    border-radius: 50%;
    animation: ${spin} 0.4s linear infinite;
  `}
`;

export const OnlineDot = styled.div<{ size?: string; isOnline?: boolean }>`
  width: ${({ size }) => size ?? '14px'};
  height: ${({ size }) => size ?? '14px'};
  border-radius: 50%;
  background: ${({ theme, isOnline }) => (isOnline ? theme.statusOnline : theme?.textSecondary)};
`;
