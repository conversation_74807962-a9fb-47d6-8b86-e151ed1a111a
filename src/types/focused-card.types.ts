import { ContentType } from 'enums/content-type.enums';

export { ContentType };

export interface FocusedCardData {
  // Common properties
  name?: string;
  title?: string;
  imageUrl?: string;
  thumbnailUrl?: string;
  bannerUrl?: string;

  // Game-specific properties
  gameLogoUrl?: string | null;
  esrbRating?: string;
  esrbRatingName?: string;
  starRating?: number;
  index?: number;
  isVertical?: boolean;
  videoUrl?: string;

  // Video-specific properties
  channelName?: string;
  channelLogo?: string;
  timeAgo?: string;
  uploadDate?: string;
  duration?: number;
  isVerified?: boolean;

  // Event-specific properties
  companyName?: string;
  companyUrl?: string;
  initialIsSaved?: boolean;
  startDate?: string;
  endDate?: string;

  // Statistics properties
  playCount?: number;
  followerCount?: number;
  favoriteCount?: number;
  genres?: string[];

  // Content type to identify the source
  contentType: ContentType;
}
