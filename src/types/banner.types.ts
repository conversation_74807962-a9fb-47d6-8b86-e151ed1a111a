/**
 * Generic banner item interface that can be extended for specific banner types
 */
export interface BaseBannerItem {
  id: string;
  title: string;
  media_url: string;
  media_type: string;
  created_at: string;
  display_order: number;
}

/**
 * Game-specific banner item interface
 */
export interface GameBannerItem extends BaseBannerItem {
  game_slug: string;
  game_id: string;
  trailer_banner: unknown[];
  offer_banner: unknown[];
  event_banner: unknown[];
  modified_by: string;
  follower_count: number;
  favorite_count: number;
  play_count: number;
  saved_count: number;
  genres: string[];
  file_type?: string;
}

/**
 * Generic banner item for HeroBanner component
 * Extends the base banner item with imageUrl for compatibility
 */
export interface HeroBannerItem<T extends BaseBannerItem = BaseBannerItem> extends BaseBannerItem {
  imageUrl: string;
  bannerData?: T;
}

/**
 * Type guard to check if an item is a GameBannerItem
 */
export const isGameBannerItem = (item: unknown): item is GameBannerItem => {
  return (
    item !== null &&
    typeof item === 'object' &&
    'game_slug' in item &&
    typeof (item as Record<string, unknown>).game_slug === 'string' &&
    'game_id' in item &&
    typeof (item as Record<string, unknown>).game_id === 'string'
  );
};

/**
 * Utility function to convert any banner item to HeroBannerItem format
 */
export const toHeroBannerItem = <T extends BaseBannerItem>(
  item: T
): HeroBannerItem & { bannerData: T } => {
  return {
    ...item,
    imageUrl: item.media_url || '',
    bannerData: item,
  };
};
