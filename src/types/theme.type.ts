export interface IColors {
  darkNavy: string;
  grey: string;
  periwinkle: string;
  phyndWhite: string;
  gold: string;
  green: string;
  red: string;
  iconWhite: string;
  offWhite: string;
  perlWhite: string;
  midnightSlate: string;
}

export interface ITheme extends IGradients {
  // Surface tokens
  bgColor: string;
  surfaceSecondaryOpacity40: string;
  surfaceSecondaryOpacity60: string;
  surfaceSecondaryOpacity100: string;
  surfaceWhite: string;
  offWhite: string;
  bgColorOpacity90: string;
  perlWhite: string;

  // Typography tokens
  textPrimary: string;
  textSecondary: string;
  textInvert: string;

  // Outline tokens
  outlineStrokeFocusWhite: string;
  outlineStrokeFocusTertiary: string;
  outlineStrokeStandardWhite: string;

  // Icon tokens
  iconGold: string;
  iconInverted: string;
  iconWhite: string;
  favorite: string;
  iconWhite40: string;

  // Status tokens
  statusOnline: string;

  // button colors
  btnSecondary: string;
}

// Interface for gradients
export interface IGradients {
  gameTileOverlayGradient: string;
  idleOverlayGradient: string;
  interstitialOverlayGradient: string;
}

export interface IColProps {
  gap?: string;
  maxWidth?: string;
  minWidth?: string;
  align?: string;
  justify?: string;
  padding?: string;
  textAlign?: string;
  width?: string;
  height?: string;
  wrap?: string;
  margin?: string;
  marginTop?: string;
  marginBottom?: string;
  marginRight?: string;
  marginLeft?: string;
  order?: number;
  self?: string;
  overflow?: string;
  flex?: string;
  flexWrap?: string;
  flexDirection?: boolean;
}

export interface IVerticalLine {
  height?: string;
  color?: string;
}

export interface ILoaderProps {
  size?: number; // Accept size in pixels
  color?: string; // Optional color
}
