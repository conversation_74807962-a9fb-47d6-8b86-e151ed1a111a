export interface IEventsOffers {
  id?: string | null;
  pub_id?: string | null;
  game_slug?: string | null;
  game_name?: string | null;
  media_url?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  title?: string;
  event_type?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  is_saved_event?: boolean | null;
  is_saved_offer?: boolean | null;
  saved_count?: number | null;
  play_count?: number;
  favorite_count?: number;
}
