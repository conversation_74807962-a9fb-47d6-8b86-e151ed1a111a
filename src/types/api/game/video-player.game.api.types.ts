export interface IVideoPlayerModel {
  game_details: IGameDetailsVideo;
  game_genres: IGameGenre[];
  game_themes: IGameTheme[];
  is_game_specific: boolean;
  media: IMedia;
  publisher: IPublisher;
  recommended_media: IRecommendedMedia;
}

export interface IGameDetailsVideo {
  description: string;
  first_release_date: number;
  game_played_count: number;
  id: string;
  name: string;
  slug: string;
  star_ratings: number;
}

export interface IGameGenre {
  description: string;
  id: string;
  image_url: string;
  is_enabled: boolean;
  name: string;
  slug: string;
  version: number;
}

export interface IGameTheme {
  description: string;
  id: string;
  image_url: string;
  is_enabled: boolean;
  name: string;
  slug: string;
  version: number;
}

export interface IMedia {
  created_at: string;
  description: string;
  duration: number;
  game_id: string;
  genre_ids: string[];
  id: string;
  media_count: number;
  publisher_id: string;
  thumbnail_url: string;
  title: string;
  url: string;
  is_favorited?: boolean; // optional because Dart model defaults it
  is_saved?: boolean; // optional because Dart model defaults it
}

export interface IPublisher {
  bio: string;
  display_name: string;
  dp_url: string;
  id: string;
  is_verified: boolean;
}

export interface IRecommendedMedia {
  created_at: string;
  duration?: number | null;
  game_name: string;
  id: string;
  is_game_specific: boolean;
  is_saved: boolean;
  is_verified?: boolean | null;
  media_url: string;
  publisher_name?: string | null;
  title: string;
  thumbnail_url?: string | null;
}

export interface IVideoPlayerRelatedVideo {
  game_id: string;
  name: string;
  star_rating: number;
  game_image?: string;
  esrb?: string;
  genre_overlap_count: number;
}

export interface IVideoGenre {
  id: string;
  slug: string;
  name: string;
  image_url?: string;
  description: string;
  is_enabled: boolean;
  version: number;
  priority: number;
}

export interface IVideoByGenre {
  id: string;
  title: string;
  duration: number;
  media_url: string;
  created_at: string;
  publisher_id: string;
  publisher_name: string;
  is_verified: boolean;
  dp_url: string;
  game_name?: string;
  game_id?: string;
  overlap_genre_count?: number;
}

export interface ITrendingVideo {
  id: string;
  title: string;
  duration: number;
  media_url: string;
  count: number;
  created_at: string;
  publisher_id: string;
  publisher_name: string;
  is_verified: boolean;
  dp_url: string;
}

export interface IVideoSceneItem {
  timestamp: number;
  url: string;
}

export interface IVideoSceneModel {
  id: string;
  thumbnail_url: IVideoSceneItem[];
  preview_clip: string;
  created_at: string;
  updated_at: string;
}
