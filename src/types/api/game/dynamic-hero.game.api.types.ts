export interface IGameInfo {
  game_slug?: string | null;
  release_year?: number | null;
  publisher_name?: string | null;
  game_name?: string | null;
  esrb?: string | null;
  play_count?: number | null;
  genre?: string | null;
  platform?: string | null;
}

export interface IBannerInfo {
  id?: string | null;
  media_url?: string | null;
  start_date?: string | null;
  end_date?: string | null;
  title?: string | null;
  is_saved_offer?: boolean | null;
  like_count?: number | null;
  description?: string | null;
  cta_url?: string | null;
  game_slug?: string | null;
}

export interface IDynamicHeroModel {
  id?: string | null;
  title?: string | null;
  pub_id?: string | null;
  media_url?: string | null;
  video_url?: string | null;
  media_type?: string | null;
  offer_id?: string | null;
  game_id?: string | null;
  trailer_id?: string | null;
  event_id?: string | null;
  modified_by?: string | null;
  created_at?: string | null;
  updated_at?: string | null;
  is_enable?: boolean | null;
  version?: number | null;
  followers?: number | null;
  gamesCount?: number | null;
  upcomingEvents?: number | null;
  dp_url?: string | null;
  is_verified?: boolean | null;
  gameTextImg?: string | null;
  releaseYear?: string | null;
  esrb?: string | null;
  friendsCount?: number | null;
  onlineCount?: number | null;
  companyName?: string | null;
  esrbText?: string | null;
  game_slug?: string | null;
  genre?: string[] | null;
  games?: IGameInfo[] | null;
  offer_banner?: IBannerInfo[] | null;
  event_banner?: IBannerInfo[] | null;
}
