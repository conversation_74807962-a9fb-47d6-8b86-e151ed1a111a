// export interface IVideoItem {
//   videoSlug: string;
//   title: string;
//   imageUrl: string;
//   videoUrl: string;
//   publisherAvatarUrl: string;
//   publisherName: string;
//   friendsWatchedCount: number;
//   createdAt: string; // ISO date string from JSON
// }
export interface IVideoItem {
  media_id: string;
  game_slug: string;
  url: string;
  title: string;
  created_at: string;
  description: string;
  media_type: string;
  company_name: string;
  game_title: string;
  organization_id: string;
  company_image: string;
  thumbnail_url: string;
  duration: number;
  is_verified: boolean;
  friends_viewed: number;
  is_saved: boolean;
  is_favorited: boolean;
  play_count?: string;
  favorite_count?: string;
}
