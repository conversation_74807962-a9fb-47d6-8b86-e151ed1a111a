import { GAME_MEDIA } from 'enums/game-media.enums';

export interface IGameMedia {
  url: string;
  title?: string | null;
  description?: string | null;
  media_type?: GAME_MEDIA | null;
  thumbnail_url?: string | null;
  duration?: number | null;
  created_at?: string | null; // Use string to represent ISO date
  id?: string | null;
  is_verified: true;
  media_id: string;
  studio_display_name_slug: string;
  studio_id: string;
  studio_name: string;
  studio_url: string;
  play_count?: string;
  favorite_count?: string;
  view_count?: number;
  // company_image?: string | null;
  // company_name?: string | null;
  // is_verified?: boolean | null;
  // friends_viewed?: number | null;
}
