import { GAME_MEDIA } from 'enums/game-media.enums';

export interface IPlatform {
  image_url?: string;
  name?: string;
}

export interface IController {
  image_url?: string;
  name?: string;
}

export interface IGameOverview {
  url: string;
  title: string | null;
  description: string | null;
  media_type: GAME_MEDIA;
}

export interface IGameScreenshot {
  url: string;
  title?: string | null;
}

export interface IGame {
  game_slug: string;
  game_title: string;
  short_bio: string;
  description: string;
  developers: string[];
  star_rating: number;
  is_browser_based_game: boolean;
  download_url: string;
  launcher_url: string | null;
  release_date: number;
  start_date: number;
  end_date: number;
  is_blockchain_supported: boolean;
  blockchain_platform: string | null;
  genre: string[];
  sub_genre: string[];
  game_theme: string[];
  ad_supported: boolean;
  content_rating: boolean;
  age_restricted: boolean;
  cost: string;
  rental_duration: number;
  syndicated: boolean;
  website_url: string;
  twitter_link: string | null;
  discord_link: string | null;
  white_paper_link: string | null;
  telegram_link: string | null;
  language_supported: string[];
  modes: string[];
  platforms: IPlatform[];
  controllers?: IController[];
  game_controllers_supported: string[];
  browser_support: string[];
  tags: string[];
  storage_requirements: string;
  ram_requirements: string;
  processor_requirements: string;
  os_requirements: string;
  game_media: IGameOverview[];
  trailers: IGameOverview[];
  game_screenshots: IGameScreenshot[];
  game_play_modes: string[];
  in_app_purchases: boolean;
  is_game_featured: boolean;
  is_from_verified_publisher: boolean;
  game_franchise: string[];
  iframable: boolean;
  publisher_id: string | null;
  publisher_display_name: string | null;
  publisher_type: string | null;
  publisher_display_name_slug: string | null;
  parent_company_id: string | null;
  parent_company_name: string | null;
  parent_company_display_name: string | null;
  parent_company_type: string | null;
  parent_company_display_name_slug: string | null;
  esrb_rating_img_url: string;
  pegi_rating_img_url: string;
  esrb_rating_name: string;
  pegi_rating_name: string;
  esrb_description: string;
  pegi_description: string;
  rainway_game_id: string | null;
  thumbnail: string;
  game_text_img_url: string;
  favorite_count: number;
  followers_count: number;
  player_count: number;
}

export interface IFavoriteGame {
  game_slug: string;
  created_at: string;
  title?: string;
  description?: string;
  image_url?: string;
  company_name?: string;
  image?: string;
  organization_id?: string;
  trailer_url?: string;
  rating?: number;
  esrb?: string;
  esrb_rating_name?: string;
  esrb_description?: string;
  released_at?: number;
  logo_url?: string;
  default_banner_media_url?: string;
  default_banner_media_type?: string;
  play_count?: string;
  follower_count?: string;
  favorite_count?: string;
  genres?: string[];
}
