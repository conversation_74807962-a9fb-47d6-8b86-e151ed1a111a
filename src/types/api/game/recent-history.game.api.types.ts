export interface IRecentHistory {
  game_slug: string;
  last_played: string; // ISO date string (convert to Date in TS if needed)
  title: string;
  description?: string | null;
  company_name?: string | null;
  company_image?: string | null;
  organization_id?: string | null;
  image_url: string;
  logo_url?: string | null;
  esrb: string;
  rating_count?: number | null;
  rating?: number | null;
  esrb_rating_name?: string | null;
  esrb_description?: string | null;
  released_at?: number | null;
  default_banner_media_url?: string | null;
  default_banner_media_type?: string | null;
  play_count?: string;
  follower_count?: string;
  favorite_count?: string;
  genres?: string[];
}
