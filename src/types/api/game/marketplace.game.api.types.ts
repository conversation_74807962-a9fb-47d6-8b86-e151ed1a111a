import { MARKETPLACE_GAME_FEATURED_TYPE } from 'enums/featured-marketplace.enums';

export interface IGameMarketplace {
  domain: string;
  image_url: string;
  name: string;
}

export interface IGameItem {
  category?: string[];
  company_image?: string;
  company_name?: string;
  company_website?: string;
  contract_id?: string;
  download_url?: string;
  esrb_rating_img_url?: string;
  first_release_date?: number;
  game_chain: string;
  iframable?: boolean;
  image?: string;
  is_browser_based_game?: boolean;
  launcher_url?: string;
  marketplaces?: IGameMarketplace[];
  mode?: string[];
  name: string;
  platform?: string[];
  publisher_display_name?: string;
  publisher_first_name?: string;
  publisher_id?: string;
  publisher_img_url?: string;
  rainway_game_id?: string;
  slug: string;
  summary?: string;
  trailer_url?: string;
  logo_url?: string;
}

export interface IGamePayload {
  game_chain?: string[];
  gameName?: string;
  network?: string;
  categories?: string[];
  sortby?: string;
  reverse?: boolean;
  external_category?: string;
  domain?: string;
  date_created?: string;
  date_modified?: string;
  iframable?: boolean;
  platform?: string[];
  status?: string;
  publisher_id?: string[];
  tags?: string[];
  playNow?: boolean;
  featured_type?: MARKETPLACE_GAME_FEATURED_TYPE[];
  studio_id?: string[];
  developer_id?: string[];
}
