import { GAME_CONTENT } from 'enums/game-content.enums';

export interface IContent {
  media_id: string;
  game_slug: string;
  url: string;
  title: string;
  created_at: string; // ISO 8601 date string
  description: string;
  media_type: GAME_CONTENT;
  company_name?: string;
  game_title: string;
  organization_id?: string;
  company_image?: string;
  thumbnail_url?: string;
  duration?: number;
  is_verified?: boolean;
  friends_viewed: number;
  is_favorited: boolean;
  is_saved: boolean;
}
