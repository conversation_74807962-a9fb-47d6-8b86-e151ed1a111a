export interface IProfile {
  user: IUser;
  social: ISocial;
}

export interface IUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  is_admin: boolean;
  dp_url: string;
  cover_image_url: string;
  is_disabled: boolean;
  is_verified: boolean;
  display_name: string;
  two_factor_auth_enable: boolean;
  bio: string;
  is_kba_enabled: boolean;
  is_secured: boolean;
  tooltip: boolean;
  timezone: string;
  timezone_offset: number;
  timezone_locale: string;
  avatar_id: string;
  is_publisher: boolean;
  is_publisher_approved: boolean;
  isOrganization: boolean;
  is_authorized: boolean;
  is_publisher_blocked: boolean;
  is_user_ban: boolean;
  is_user_blocked: boolean;
  user_ban_reason: string;
  user_blocked_reason: string;
  publisher_blocked_reason: string;
  applied_for_publisher: boolean;
  logged_in_as: string;
  company_detail_added: boolean;
  joined_as_user_on: string;
  joined_as_publisher_on: string;
  company_details: ICompanyDetails;
  publisher_request_status: string;
  roles: string[];
  is_publisher_ban: boolean;
}

export interface ICompanyDetails {
  id: string;
  company_name: string;
  company_metaphone: string;
  website: string;
  about: string;
  suffix: string;
  created_at: string;
  modified_at: string;
  created_by: string;
  image: string;
  is_individual: boolean;
  org_type: string;
  parent_id: string;
}

export interface ISocial {
  user_id: string;
  instagram_profile: string;
  discord_profile: string;
  twitter_profile: string;
  website: string;
  youtube: string;
  google: string;
  meta: string;
  version: number;
}
