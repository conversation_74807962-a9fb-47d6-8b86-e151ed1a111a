import { FRIEND_STATUS } from 'enums/friend.enums';

export interface IFriendItem {
  id: string;
  user_id: string;
  friend_id: string;
  friend_first_name: string;
  friend_last_name: string;
  friend_display_name: string;
  friend_display_name_slug: string;
  friend_dp_url?: string;
  created_at: string;
}

export interface IProfileStatsRes {
  friend_count?: number | null;
  id?: string | null;
  mutual_friend_count?: number | null;
  user_followers?: number | null;
  user_followings?: number | null;
}

export interface IFriendStatsRes {
  id: string;
  status: FRIEND_STATUS;
}
