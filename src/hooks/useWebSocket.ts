import { useCallback, useEffect, useRef, useState } from 'react';

interface I_useWebSocket {
  socketURL: string;
  socketName?: string;
  onOpen?: (event: MessageEvent<any>) => void;
  autoConnect?: boolean; // default is true
  onConnect?: (event: Event) => void;
  disablePing?: boolean;
}

export const useWebSocket = ({
  socketURL,
  socketName,
  onOpen,
  onConnect,
  autoConnect = true,
  disablePing,
}: I_useWebSocket) => {
  const [connected, setConnected] = useState(false);
  const connRef = useRef<WebSocket | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const clearPingInterval = () => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  };

  const connectSocket = useCallback(() => {
    const ws = new WebSocket(socketURL);
    connRef.current = ws;

    ws.onopen = event => {
      console.log(`${socketName ?? ''} WebSocket connection established successfully`);
      setConnected(true);
      onConnect?.(event);

      if (disablePing) {
        return;
      }

      pingIntervalRef.current = setInterval(() => {
        ws.send(JSON.stringify({ message: 'PING' }));
      }, 9000);
    };

    ws.onmessage = event => {
      console.log('message----WS', event);
      try {
        const data = JSON.parse(event.data);
        onOpen?.({ ...event, data });
      } catch (error) {
        console.log(error);
        onOpen?.(event);
      }
    };

    ws.onclose = () => {
      console.log(`${socketName ?? ''} WebSocket connection is closed`);
      setConnected(false);
      clearPingInterval();
    };

    ws.onerror = error => {
      console.error(`${socketName ?? ''} WebSocket error`, error);
      setConnected(false);
    };
  }, [socketURL, socketName, onOpen]);

  const disconnectSocket = useCallback(() => {
    if (connRef.current?.readyState === WebSocket.OPEN) {
      connRef.current.close();
    }
    clearPingInterval();
  }, []);

  const emitWSEvent = useCallback((event: any) => {
    if (connRef.current?.readyState === WebSocket.OPEN) {
      connRef.current.send(JSON.stringify(event));
    } else {
      console.error('WebSocket is not open. ReadyState:', connRef.current?.readyState);
    }
  }, []);

  useEffect(() => {
    if (autoConnect === false) {
      return;
    }

    const conn = new WebSocket(socketURL);

    let pingInterVal: NodeJS.Timeout;

    conn.onopen = event => {
      console.log(`${socketName ?? ''} Web socket connection established successful`);
      setConnected(true);
      onConnect?.(event);
      if (disablePing) {
        return;
      }
      pingInterVal = setInterval(() => conn.send(JSON.stringify({ message: 'PING' })), 9000);
    };

    conn.addEventListener('message', event => {
      console.log('message----WS', event);
      // handleSocketEvent(event)
      try {
        const data = JSON.parse(event?.data);
        onOpen?.({ ...event, data });
      } catch (error) {
        onOpen?.(event);
      }
    });

    conn.addEventListener('close', () => {
      console.log(`${socketName ?? ''} Web socket connection is closed`);
      setConnected(false);
    });

    return () => {
      conn.close();
      setConnected(false);
      clearInterval(pingInterVal);
    };
  }, []);

  return {
    connected,
    connectSocket,
    emitWSEvent,
    disconnectSocket,
  };
};
