import {
  getScreenSaverAnimationSetting,
  getScreenSavers,
  getScreenSaverSetting,
  getUserStatusWebSocket,
} from 'api/game-service/game-service.api';
import idlePageConfig from 'config/idle-page.config';
import { useAppDispatch, useAppSelector } from 'hooks/redux.hooks';
import { useRemoteControl } from 'hooks/useRemoteControl';
import { useWebSocket } from 'hooks/useWebSocket';
import React, {
  createContext,
  useMemo,
  useState,
  ReactNode,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import ReactDOM from 'react-dom';
import { useLocation } from 'react-router-dom';
import { AppIdle } from 'shared/appIdle';
import { setProfileOnlineStatus } from 'state-management/redux/actions/profile.action';
import { IScreenSaverAnimationSetting } from 'types/api/game/screensaver-settings.game.api.types';
import { IScreenSaver } from 'types/api/game/screensaver.game.api.types';

const IdleScreenContext = createContext({});

const USER_IDLE_ONLINE_TIMEOUT = 10;

interface IThemeContextProviderProps {
  children: ReactNode;
}

export const IdleScreenContextProvider: React.FC<IThemeContextProviderProps> = ({ children }) => {
  const location = useLocation();
  const { isLoggedIn } = useAppSelector(state => state.user);

  const [showIdleScreen, setShowIdleScreen] = useState(false);
  const [timeoutSeconds, setTimeoutSeconds] = useState<number | null>(null);
  const [screenSaverList, setScreenSaverList] = useState<IScreenSaver[]>([]);
  const [isLoop, setIsLoop] = useState<boolean>(true);
  const [animDuration, setAnimDuration] = useState<IScreenSaverAnimationSetting>();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutOnlineRef = useRef<NodeJS.Timeout | null>(null);

  const pathname = location.pathname;

  const basePath = ('/' + pathname.split('/')[1]) as keyof typeof idlePageConfig;

  const pageId = idlePageConfig[basePath];

  const dispatch = useAppDispatch();
  const { isOnline } = useAppSelector(state => state?.user);

  const { connectSocket, disconnectSocket } = useWebSocket({
    socketURL: getUserStatusWebSocket(),
    autoConnect: false,
    disablePing: true,
  });

  // Function to start the idle timer
  const startIdleTimer = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (timeoutSeconds && timeoutSeconds > 0) {
      timeoutRef.current = setTimeout(() => {
        setShowIdleScreen(true);
      }, timeoutSeconds * 1000);
    }
  }, [timeoutSeconds]);

  // Function to reset the idle timer (called when user is active)
  const resetIdleTimer = useCallback(() => {
    setShowIdleScreen(false);
    startIdleTimer();
  }, [startIdleTimer]);

  const startOnlineIdleTimer = useCallback(() => {
    if (timeoutOnlineRef.current) {
      clearTimeout(timeoutOnlineRef.current);
    }

    timeoutOnlineRef.current = setTimeout(
      () => {
        disconnectSocket();
        dispatch(setProfileOnlineStatus(false));
      },
      USER_IDLE_ONLINE_TIMEOUT * 60 * 1000
    );
  }, [disconnectSocket, dispatch]);

  // Function to reset the idle timer (called when user is active)
  const resetOnlineIdleTimer = useCallback(() => {
    if (!isOnline) {
      connectSocket();
      dispatch(setProfileOnlineStatus(true));
    }

    startOnlineIdleTimer();
  }, [startOnlineIdleTimer, connectSocket, isOnline, dispatch]);

  const onkeydown = () => {
    resetIdleTimer();
    resetOnlineIdleTimer();
  };

  useRemoteControl({
    onKeyDown: onkeydown,
  });

  // Fetch screensaver settings when user logs in
  useEffect(() => {
    if (isLoggedIn) {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setShowIdleScreen(false);
      setTimeoutSeconds(null);

      getScreenSaverSetting(pageId).then(res => {
        const timeout = res.time_out_seconds ?? 1;
        setTimeoutSeconds(timeout);
      });
      connectSocket();
      dispatch(setProfileOnlineStatus(true));
      startOnlineIdleTimer();
    } else {
      // Clear timer and reset state when user logs out
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setShowIdleScreen(false);
      setTimeoutSeconds(null);
    }

    return () => {
      disconnectSocket();
      dispatch(setProfileOnlineStatus(false));
    };
  }, [isLoggedIn, dispatch, disconnectSocket, startOnlineIdleTimer, connectSocket, pageId]);

  // Start timer when timeout settings are available
  useEffect(() => {
    if (isLoggedIn && timeoutSeconds && !showIdleScreen) {
      startIdleTimer();
    }

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isLoggedIn, timeoutSeconds, showIdleScreen, startIdleTimer]);

  const handleFetchScreenSaver = async (screenIds: number[] = []) => {
    try {
      const { isLoop, screensavers } = await getScreenSavers(screenIds);
      setScreenSaverList(screensavers);
      setIsLoop(isLoop);
      const data = await getScreenSaverAnimationSetting();
      setAnimDuration(data);
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    if (isLoggedIn) {
      handleFetchScreenSaver();
    }
  }, [isLoggedIn]);

  const contextValue = useMemo(
    () => ({
      showIdleScreen,
      resetIdleTimer,
    }),
    [showIdleScreen, resetIdleTimer]
  );

  return (
    <IdleScreenContext.Provider value={contextValue}>
      {showIdleScreen &&
        ReactDOM.createPortal(
          <AppIdle
            isLoop={isLoop}
            screensaverList={screenSaverList}
            pageId={pageId}
            animDuration={animDuration}
            onScreenSaverEnd={() => {
              handleFetchScreenSaver(screenSaverList.map(item => item.image_no!));
            }}
          />,
          document.body
        )}
      {children}
    </IdleScreenContext.Provider>
  );
};
