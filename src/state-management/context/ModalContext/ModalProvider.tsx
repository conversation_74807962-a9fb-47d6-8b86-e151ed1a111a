import React, { useState, ReactNode, useMemo, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Overlay } from './style';
import { useRemoteControl } from 'hooks/useRemoteControl';
import { ModalContext } from './ModalContext';

export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [modalContent, setModalContent] = useState<ReactNode | null>(null);

  useRemoteControl({
    onBack: () => {
      hideModal();
    },
  });

  React.useEffect(() => {
    const handleShowModal = (event: CustomEvent) => {
      setModalContent(event.detail);
    };

    window.addEventListener('showModal', handleShowModal as EventListener);
    return () => {
      window.removeEventListener('showModal', handleShowModal as EventListener);
    };
  }, []);

  const showModal = useCallback((content: ReactNode) => {
    setModalContent(content);
  }, []);

  const hideModal = useCallback(() => {
    setModalContent(null);
  }, []);

  const modalValue = useMemo(() => ({ showModal, hideModal }), [showModal, hideModal]);

  return (
    <ModalContext.Provider value={modalValue}>
      {children}
      {modalContent &&
        createPortal(<Overlay id="active-modal-container">{modalContent}</Overlay>, document.body)}
    </ModalContext.Provider>
  );
};
