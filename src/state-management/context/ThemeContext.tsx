import React, { createContext, useMemo, useState, ReactNode } from 'react';

import { ThemeProvider } from 'styled-components';
import { Themes } from 'enums/theme.enum';
import { GlobalStyle } from 'styles/globalStyle';
import { getTheme } from 'styles/theme/darkTheme';
import { Toaster } from 'react-hot-toast';

const darkTheme = { ...getTheme(), selected: Themes.DARK };

interface IThemeContext {
  currentTheme: typeof darkTheme;
}

const ThemeContext = createContext<IThemeContext>({
  currentTheme: darkTheme,
});

interface IThemeContextProviderProps {
  children: ReactNode;
}

export const ThemeContextProvider: React.FC<IThemeContextProviderProps> = ({ children }) => {
  const [currentTheme] = useState(darkTheme);

  const contextValue = useMemo(() => ({ currentTheme }), [currentTheme]);

  return (
    <ThemeContext.Provider value={contextValue}>
      <ThemeProvider theme={currentTheme}>
        <GlobalStyle />
        {children}

        <Toaster
          toastOptions={{
            className: '',
            style: {
              fontFamily: 'Roboto',
              fontStyle: 'normal',
              fontWeight: 600,
              fontSize: '20px',
              lineHeight: ' 21px',
              zIndex: 9999999,
            },
            error: {
              style: {
                padding: '20px',
                background: currentTheme.surfaceWhite,
                color: currentTheme.bgColor,
              },
            },
            success: {
              style: {
                padding: '20px',
                background: currentTheme.surfaceWhite,
                color: currentTheme.bgColor,
              },
            },
          }}
          containerStyle={{
            top: '75px',
            right: '20px',
            zIndex: 99999999,
          }}
          position="top-center"
          reverseOrder={true}
        />
      </ThemeProvider>
    </ThemeContext.Provider>
  );
};
