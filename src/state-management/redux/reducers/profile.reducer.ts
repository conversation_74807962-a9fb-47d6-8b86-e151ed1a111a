import { createReducer } from '@reduxjs/toolkit';

import { IUserState } from 'types/reducer/user.reducer.type';
import {
  getProfileDetails,
  setLoginStatus,
  setProfileLoading,
  setProfileOnlineStatus,
} from '../actions/profile.action';

export const initialState: IUserState = {
  profile: null,
  loading: false,
  isLoggedIn: false,
  isOnline: false,
};

const userReducer = createReducer(initialState, builder => {
  builder
    .addCase(setProfileLoading, (state, action) => {
      state.loading = action.payload;
    })
    .addCase(setLoginStatus, (state, action) => {
      state.isLoggedIn = action.payload;
    })
    .addCase(getProfileDetails.fulfilled, (state, action) => {
      state.profile = action.payload;
    })
    .addCase(setProfileOnlineStatus, (state, action) => {
      state.isOnline = action.payload;
    });
});

export default userReducer;
