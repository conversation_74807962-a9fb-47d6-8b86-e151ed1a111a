import { createAction, createAsyncThunk } from '@reduxjs/toolkit';
import { getUserMyDetails } from 'api/user-service/user-service.api';
import { IProfile } from 'types/api/user/profile.user.api.types';

export const setProfileLoading = createAction<boolean>('profile/loading');

export const setLoginStatus = createAction<boolean>('profile/login');

export const setProfileOnlineStatus = createAction<boolean>('profile/online-status');

export const getProfileDetails = createAsyncThunk<IProfile | null>(
  'profile/profileDetail',
  async (_, thunkAPI) => {
    thunkAPI.dispatch(setProfileLoading(true));

    try {
      const profile = await getUserMyDetails();
      thunkAPI.dispatch(setLoginStatus(true));
      return profile; // Type: IProfileRes
    } catch (error) {
      console.error(error);
      thunkAPI.dispatch(setLoginStatus(false));
      return null; // Explicitly return null on failure
    } finally {
      thunkAPI.dispatch(setProfileLoading(false));
    }
  }
);
