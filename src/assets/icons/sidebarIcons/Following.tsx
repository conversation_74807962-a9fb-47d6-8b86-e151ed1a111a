import * as React from 'react';

interface FollowingIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const FollowingIcon: React.FC<FollowingIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    className={className}
    {...props}
  >
    <path
      d="M5 11.25C5.99456 11.25 6.94839 10.8549 7.65165 10.1517C8.35491 9.44839 8.75 8.49456 8.75 7.5C8.75 6.50544 8.35491 5.55161 7.65165 4.84835C6.94839 4.14509 5.99456 3.75 5 3.75C4.00544 3.75 3.05161 4.14509 2.34835 4.84835C1.64509 5.55161 1.25 6.50544 1.25 7.5C1.25 8.49456 1.64509 9.44839 2.34835 10.1517C3.05161 10.8549 4.00544 11.25 5 11.25ZM15 5H12.5V10H15H37.5H40V5H37.5H15ZM15 17.5H12.5V22.5H15H37.5H40V17.5H37.5H15ZM15 30H12.5V35H15H37.5H40V30H37.5H15ZM5 36.25C5.99456 36.25 6.94839 35.8549 7.65165 35.1517C8.35491 34.4484 8.75 33.4946 8.75 32.5C8.75 31.5054 8.35491 30.5516 7.65165 29.8483C6.94839 29.1451 5.99456 28.75 5 28.75C4.00544 28.75 3.05161 29.1451 2.34835 29.8483C1.64509 30.5516 1.25 31.5054 1.25 32.5C1.25 33.4946 1.64509 34.4484 2.34835 35.1517C3.05161 35.8549 4.00544 36.25 5 36.25ZM8.75 20C8.75 19.5075 8.653 19.0199 8.46455 18.5649C8.27609 18.11 7.99987 17.6966 7.65165 17.3483C7.30343 17.0001 6.89003 16.7239 6.43506 16.5355C5.98009 16.347 5.49246 16.25 5 16.25C4.50754 16.25 4.01991 16.347 3.56494 16.5355C3.10997 16.7239 2.69657 17.0001 2.34835 17.3483C2.00013 17.6966 1.72391 18.11 1.53545 18.5649C1.347 19.0199 1.25 19.5075 1.25 20C1.25 20.4925 1.347 20.9801 1.53545 21.4351C1.72391 21.89 2.00013 22.3034 2.34835 22.6517C2.69657 22.9999 3.10997 23.2761 3.56494 23.4645C4.01991 23.653 4.50754 23.75 5 23.75C5.49246 23.75 5.98009 23.653 6.43506 23.4645C6.89003 23.2761 7.30343 22.9999 7.65165 22.6517C7.99987 22.3034 8.27609 21.89 8.46455 21.4351C8.653 20.9801 8.75 20.4925 8.75 20Z"
      fill={fill}
    />
  </svg>
);

export default FollowingIcon;
