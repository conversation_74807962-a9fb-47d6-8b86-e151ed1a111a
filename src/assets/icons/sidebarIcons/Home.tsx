import * as React from 'react';

interface HomeIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const HomeIcon: React.FC<HomeIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_2015_2074)">
      <path
        d="M39.6875 17.5H37.8125C37.6406 17.5 37.5 17.6406 37.5 17.8125V19.0625H35.625V17.8125C35.625 17.6406 35.4844 17.5 35.3125 17.5H33.4375C33.2656 17.5 33.125 17.6406 33.125 17.8125V19.0625H31.25V17.8125C31.25 17.6406 31.1094 17.5 30.9375 17.5H29.0625C28.8906 17.5 28.75 17.6406 28.75 17.8125V22.1875L30 24.0625V26.25H25.625V15H27.5C27.8438 15 27.9259 14.8012 27.6831 14.5581L20.4422 7.31687C20.4034 7.27913 20.3598 7.24667 20.3125 7.22031V3.53438C22.7084 4.12813 25.1041 0.936875 27.5 1.875C25.1041 -0.0615625 22.7084 2.13188 20.3125 0.539688V0.3125C20.3125 0.22962 20.2796 0.150134 20.221 0.0915291C20.1624 0.032924 20.0829 0 20 0C19.9171 0 19.8376 0.032924 19.779 0.0915291C19.7204 0.150134 19.6875 0.22962 19.6875 0.3125V7.22063C19.6405 7.24698 19.5971 7.27934 19.5584 7.31687L12.3169 14.5581C12.0738 14.8012 12.1562 15 12.5 15H14.375V26.25H10V24.0625L11.25 22.1875V17.8125C11.25 17.6406 11.1094 17.5 10.9375 17.5H9.0625C8.89062 17.5 8.75 17.6406 8.75 17.8125V19.0625H6.875V17.8125C6.875 17.6406 6.73438 17.5 6.5625 17.5H4.6875C4.51562 17.5 4.375 17.6406 4.375 17.8125V19.0625H2.5V17.8125C2.5 17.6406 2.35938 17.5 2.1875 17.5H0.3125C0.140625 17.5 0 17.6406 0 17.8125V22.1875L1.25 24.0625V39.375C1.25 39.7188 1.53125 40 1.875 40H16.5625V34.0628C16.5625 32.165 18.1016 30.6253 20 30.6253C21.8978 30.6253 23.4375 32.165 23.4375 34.0628V40H38.125C38.4688 40 38.75 39.7188 38.75 39.375V24.0625L40 22.1875V17.8125C40 17.6406 39.8594 17.5 39.6875 17.5ZM21.875 22.5H18.125V18.7503C18.125 18.253 18.3225 17.7761 18.6742 17.4245C19.0258 17.0729 19.5027 16.8753 20 16.8753C20.4973 16.8753 20.9742 17.0729 21.3258 17.4245C21.6775 17.7761 21.875 18.253 21.875 18.7503V22.5Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_2015_2074">
        <rect width={40} height={40} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default HomeIcon;
