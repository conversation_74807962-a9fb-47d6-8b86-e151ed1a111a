import * as React from 'react';

interface LibraryIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const LibraryIcon: React.FC<LibraryIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 42"
    fill="none"
    className={className}
    {...props}
  >
    <rect y={14.0007} width={40} height={27.9996} fill={fill} />
    <rect x={3} y={6.99878} width={34} height={3.99994} fill={fill} />
    <rect x={8} y={0.000244141} width={24} height={3.99994} fill={fill} />
    <path
      d="M24.65 19.9999H15.35C12.4 19.9999 10 22.3586 10 25.2578V30.7417C10 33.6409 12.4 35.9996 15.35 35.9996H24.65C27.6 35.9996 30 33.6409 30 30.7417V25.2578C30 22.3586 27.6 19.9999 24.65 19.9999ZM22.5 26.1914C22.5 25.6509 22.95 25.2086 23.5 25.2086C24.05 25.2086 24.5 25.6509 24.5 26.1914C24.5 26.732 24.05 27.184 23.5 27.184C22.95 27.184 22.5 26.7516 22.5 26.2111V26.1914ZM18.13 30.1717C17.98 30.3191 17.79 30.3879 17.6 30.3879C17.41 30.3879 17.22 30.3191 17.07 30.1717L16.04 29.1594L15.05 30.1324C14.9 30.2798 14.71 30.3486 14.52 30.3486C14.33 30.3486 14.14 30.2798 13.99 30.1324C13.7 29.8474 13.7 29.3757 13.99 29.0906L14.98 28.1177L14.02 27.1742C13.73 26.8892 13.73 26.4175 14.02 26.1325C14.31 25.8475 14.79 25.8475 15.08 26.1325L16.04 27.0759L17.03 26.103C17.32 25.818 17.8 25.818 18.09 26.103C18.38 26.388 18.38 26.8597 18.09 27.1447L17.1 28.1177L18.13 29.13C18.42 29.415 18.42 29.8867 18.13 30.1717ZM21.54 29.1201C20.99 29.1201 20.53 28.6779 20.53 28.1373C20.53 27.5968 20.97 27.1546 21.52 27.1546H21.54C22.09 27.1546 22.54 27.5968 22.54 28.1373C22.54 28.6779 22.1 29.1201 21.54 29.1201ZM23.5 31.0562C22.95 31.0562 22.5 30.6238 22.5 30.0833V30.0636C22.5 29.5231 22.95 29.0808 23.5 29.0808C24.05 29.0808 24.5 29.5231 24.5 30.0636C24.5 30.6041 24.06 31.0562 23.5 31.0562ZM25.48 29.1201C24.93 29.1201 24.47 28.6779 24.47 28.1373C24.47 27.5968 24.91 27.1546 25.46 27.1546H25.48C26.03 27.1546 26.48 27.5968 26.48 28.1373C26.48 28.6779 26.04 29.1201 25.48 29.1201Z"
      fill="#313544"
    />
  </svg>
);
export default LibraryIcon;
