import * as React from 'react';

interface FriendsIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const FriendsIcon: React.FC<FriendsIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    className={className}
    {...props}
  >
    <path
      d="M31.1134 11.2737C30.9732 11.2542 30.833 11.2542 30.6928 11.2737C27.5884 11.176 25.125 8.69464 25.125 5.64663C25.125 2.54001 27.7086 0 30.9131 0C34.0976 0 36.7012 2.52047 36.7012 5.64663C36.6812 8.69464 34.2177 11.176 31.1134 11.2737Z"
      fill={fill}
    />
    <path
      d="M37.6457 24.814C35.4025 26.2794 32.2581 26.8265 29.354 26.4552C30.1151 24.8531 30.5157 23.0751 30.5357 21.1994C30.5357 19.2455 30.0951 17.3894 29.2539 15.7677C32.2181 15.3769 35.3625 15.924 37.6256 17.3894C40.7901 19.4214 40.7901 22.7625 37.6457 24.814Z"
      fill={fill}
    />
    <path
      d="M8.90373 11.2737C9.04393 11.2542 9.18413 11.2542 9.32432 11.2737C12.4287 11.176 14.8921 8.69464 14.8921 5.64663C14.8921 2.52047 12.3085 0 9.10401 0C5.91955 0 3.33594 2.52047 3.33594 5.64663C3.33594 8.69464 5.79939 11.176 8.90373 11.2737Z"
      fill={fill}
    />
    <path
      d="M9.12277 21.199C9.12277 23.0943 9.54336 24.8918 10.3044 26.5135C7.48047 26.8066 4.53635 26.2204 2.37332 24.8332C-0.791107 22.7816 -0.791107 19.4406 2.37332 17.389C4.51632 15.9822 7.54056 15.4156 10.3845 15.7282C9.56339 17.3695 9.12277 19.2256 9.12277 21.199Z"
      fill={fill}
    />
    <path
      d="M20.2828 27.0998C20.1226 27.0803 19.9423 27.0803 19.7621 27.0998C16.0769 26.9826 13.1328 24.0323 13.1328 20.3981C13.1528 16.6858 16.2171 13.6769 20.0425 13.6769C23.8478 13.6769 26.9321 16.6858 26.9321 20.3981C26.9121 24.0323 23.988 26.9826 20.2828 27.0998Z"
      fill={fill}
    />
    <path
      d="M13.7721 31.1442C10.7478 33.1176 10.7478 36.361 13.7721 38.3148C17.2169 40.5617 22.8648 40.5617 26.3096 38.3148C29.3339 36.3414 29.3339 33.098 26.3096 31.1442C22.8848 28.8973 17.2369 28.8973 13.7721 31.1442Z"
      fill={fill}
    />
  </svg>
);

export default FriendsIcon;
