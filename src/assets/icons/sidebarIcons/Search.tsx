import * as React from 'react';

interface SearchIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const SearchIcon: React.FC<SearchIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_2015_6076)">
      <path
        d="M32.5 16.25C32.5 19.8359 31.3359 23.1484 29.375 25.8359L38.0156 34.4844L39.7891 36.25L36.25 39.7891L34.4844 38.0234L25.8359 29.375C23.1484 31.3438 19.8359 32.5 16.25 32.5C7.27344 32.5 0 25.2266 0 16.25C0 7.27344 7.27344 0 16.25 0C25.2266 0 32.5 7.27344 32.5 16.25ZM16.25 27.5C17.7274 27.5 19.1903 27.209 20.5552 26.6436C21.9201 26.0783 23.1603 25.2496 24.205 24.205C25.2496 23.1603 26.0783 21.9201 26.6436 20.5552C27.209 19.1903 27.5 17.7274 27.5 16.25C27.5 14.7726 27.209 13.3097 26.6436 11.9448C26.0783 10.5799 25.2496 9.33971 24.205 8.29505C23.1603 7.25039 21.9201 6.42172 20.5552 5.85636C19.1903 5.29099 17.7274 5 16.25 5C14.7726 5 13.3097 5.29099 11.9448 5.85636C10.5799 6.42172 9.33971 7.25039 8.29505 8.29505C7.25039 9.33971 6.42172 10.5799 5.85635 11.9448C5.29099 13.3097 5 14.7726 5 16.25C5 17.7274 5.29099 19.1903 5.85635 20.5552C6.42172 21.9201 7.25039 23.1603 8.29505 24.205C9.33971 25.2496 10.5799 26.0783 11.9448 26.6436C13.3097 27.209 14.7726 27.5 16.25 27.5Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_2015_6076">
        <rect width={40} height={40} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default SearchIcon;
