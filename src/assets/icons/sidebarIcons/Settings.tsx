import * as React from 'react';

interface SettingsIconProps {
  fill?: string;
  width?: number;
  height?: number;
  className?: string;
}

const SettingsIcon: React.FC<SettingsIconProps> = ({
  fill = '#F5F5F5',
  width = 40,
  height = 40,
  className,
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 40 40"
    fill="none"
    className={className}
    {...props}
  >
    <g clipPath="url(#clip0_2015_3948)">
      <path
        d="M15.625 0H24.375L25.7188 6.125C26.9531 6.63281 28.1094 7.30469 29.1562 8.10938L35.1328 6.20312L39.5078 13.7812L34.8672 18.0078C34.9531 18.6562 35 19.3203 35 19.9922C35 20.6641 34.9531 21.3281 34.8672 21.9766L39.5078 26.2031L35.1328 33.7812L29.1562 31.875C28.1094 32.6797 26.9531 33.3516 25.7188 33.8594L24.375 40H15.625L14.2812 33.875C13.0469 33.3672 11.8906 32.6953 10.8437 31.8906L4.86719 33.7891L0.492188 26.2109L5.13281 21.9844C5.04687 21.3359 5 20.6719 5 20C5 19.3281 5.04687 18.6641 5.13281 18.0156L0.492188 13.7891L4.86719 6.21094L10.8437 8.11719C11.8906 7.3125 13.0469 6.64062 14.2812 6.13281L15.625 0ZM20 26.25C21.6576 26.25 23.2473 25.5915 24.4194 24.4194C25.5915 23.2473 26.25 21.6576 26.25 20C26.25 18.3424 25.5915 16.7527 24.4194 15.5806C23.2473 14.4085 21.6576 13.75 20 13.75C18.3424 13.75 16.7527 14.4085 15.5806 15.5806C14.4085 16.7527 13.75 18.3424 13.75 20C13.75 21.6576 14.4085 23.2473 15.5806 24.4194C16.7527 25.5915 18.3424 26.25 20 26.25Z"
        fill={fill}
      />
    </g>
    <defs>
      <clipPath id="clip0_2015_3948">
        <rect width={40} height={40} fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default SettingsIcon;
