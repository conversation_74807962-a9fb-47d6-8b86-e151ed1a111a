{"name": "phynd-tv-app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider vite build &&  cp -r tizen/* build && tizen build-web -- build && tizen package -t wgt -s TizenCertificate  -- build/.buildResult", "deploy": "tizen install -n Phynd.wgt -- build/.buildResult", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write .", "format:check": "prettier --check .", "format:src": "prettier --write src/", "analyzeSonar": "node ./sonarqube/sonarscan.js", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage 2>&1 | sed -n '/% Coverage report from v8/,/=================================================================================/p' > coverage.txt"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@types/lodash": "^4.17.20", "@types/styled-components": "^5.1.34", "axios": "^1.11.0", "framer-motion": "^12.23.12", "lodash": "^4.17.21", "moment-timezone": "^0.6.0", "polished": "^4.3.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.1", "recompose": "^0.30.0", "secure-ls": "^2.0.0", "shx": "^0.4.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/js": "^9.30.1", "@noriginmedia/norigin-spatial-navigation": "^2.3.0", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/moment-timezone": "^0.5.13", "@types/node": "^24.1.0", "@types/prop-types": "^15.7.15", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-redux": "^7.1.34", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "3.2.4", "@vitest/ui": "^3.2.4", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "prettier": "^3.6.2", "sonarqube-scanner": "^4.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}}