import { createRequire } from 'module';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const require = createRequire(import.meta.url);
const scanner = require('sonarqube-scanner').default;

// Load environment variables from .env file
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const envPath = join(__dirname, '..', '.env');

try {
  const envFile = readFileSync(envPath, 'utf8');
  const envLines = envFile.split('\n');

  envLines.forEach(line => {
    const trimmedLine = line.trim();
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const [key, ...valueParts] = trimmedLine.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=');
        process.env[key.trim()] = value.trim();
      }
    }
  });
} catch (error) {
  console.error('Error loading .env file:', error.message);
}

const projectName = 'tv-react-tizen-phynd';
const token = process.env.REACT_APP_SONAR_TOKEN;

console.log(token);

scanner(
  {
    serverUrl: 'http://localhost:9000',
    token: token,
    options: {
      'sonar.projectName': projectName,
      'sonar.projectDescription': 'Here I can add a description of my project',
      'sonar.projectKey': projectName,
      'sonar.projectVersion': '0.0.1',
      'sonar.exclusions': '',
      'sonar.sourceEncoding': 'UTF-8',
    },
  },
  error => {
    if (error) {
      console.error(error);
    }
    process.exit();
  }
);
