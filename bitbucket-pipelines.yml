image: node:22.12.0  

clone:
  depth: full

definitions:
  services:
    docker:
      memory: 3072

pipelines:
  branches:
    dev:
      - step:
          name: Test Coverage
          size: 4x
          script:
            - yarn install
            - export TZ="Asia/Kolkata"
            - export REACT_APP_BLOCK_EXPLORE_URL_GOERLI=https://goerli.etherscan.io
            - export REACT_APP_BLOCK_EXPLORE_URL_SEPOLIA=https://sepolia.etherscan.io
            - export REACT_APP_BLOCK_EXPLORE_URL_ETHEREUM=https://etherscan.io
            - export REACT_APP_BLOCK_EXPLORE_URL_SOLANA_DEVNET=https://explorer.solana.com
            - export REACT_APP_BLOCK_EXPLORE_URL_POLYGON=https://polygonscan.com
            - export REACT_APP_SOLANA_EXPLORER_URL=https://explorer.solana.com/tx/
            - export REACT_APP_ETHEREUM_EXPLORER_URL=https://sepolia.etherscan.io/tx/
            - yarn test:coverage 
            - mv ./coverage/lcov.info ./lcov.info
          artifacts:
            - lcov.info
      - step:
          name: SonarQube analysis
          services:
            - docker
          size: 2x
          script:
            - pipe: sonarsource/sonarqube-scan:2.0.1
              variables:
                SONAR_HOST_URL: ${SONAR_HOST_URL}
                SONAR_TOKEN: ${SONAR_TOKEN}
                EXTRA_ARGS: >
                  -Dsonar.projectKey=${SONAR_PROJECT_KEY}
                  -Dsonar.projectVersion=1.0.0
                  -Dsonar.javascript.node.maxspace=10024 
                  -Dsonar.javascript.lcov.reportPaths=./lcov.info
                  -Dsonar.coverage.exclusions=src/**/*.test.*
